'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Loading } from '@/components/ui/Loading';
import {
  Users,
  Home,
  Shield,
  TrendingUp,
  DollarSign,
  Eye,
  Star,
  MessageSquare,
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Activity,
  UserCheck,
  Building,
  Hammer,
  Briefcase,
  Settings,
  Download,
  RefreshCw,
} from 'lucide-react';
import { adminService, DashboardAnalytics } from '@/services/admin.service';
import { formatCurrency, formatRelativeTime } from '@/lib/utils';
import Link from 'next/link';

interface AdminDashboardProps {
  user: any;
}

export function AdminDashboard({ user }: AdminDashboardProps) {
  const [dashboardData, setDashboardData] = useState<DashboardAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const [statsResponse, revenueResponse, userGrowthResponse, transactionResponse] = await Promise.all([
        adminService.getDashboardStats(),
        adminService.getRevenueAnalytics(),
        adminService.getUserGrowthMetrics(),
        adminService.getTransactionAnalytics(),
      ]);

      if (statsResponse.success && revenueResponse.success && userGrowthResponse.success && transactionResponse.success) {
        setDashboardData({
          stats: statsResponse.data,
          revenue: revenueResponse.data,
          userGrowth: userGrowthResponse.data,
          transactions: transactionResponse.data,
        });
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Use mock data for demonstration
      setDashboardData({
        stats: {
          totalUsers: 12450,
          activeUsers: 8920,
          totalBrokers: 340,
          verifiedBrokers: 280,
          totalContractors: 890,
          verifiedContractors: 720,
          totalSites: 5680,
          activeSites: 3240,
          totalProjects: 2150,
          activeProjects: 890,
          pendingVerifications: 45,
          totalRevenue: 15600000,
          monthlyRevenue: 2850000,
          systemHealth: 'healthy',
        },
        revenue: {
          totalRevenue: 15600000,
          monthlyRevenue: 2850000,
          yearlyRevenue: 34200000,
          revenueGrowth: 15.2,
          revenueByCategory: [
            { category: 'Site Listings', amount: 8500000, percentage: 54.5 },
            { category: 'Project Management', amount: 4200000, percentage: 26.9 },
            { category: 'Professional Services', amount: 2900000, percentage: 18.6 },
          ],
          monthlyRevenueData: [],
        },
        userGrowth: {
          totalUsers: 12450,
          newUsersThisMonth: 890,
          userGrowthRate: 12.5,
          activeUsersRate: 71.6,
          usersByRole: [
            { role: 'site_owner', count: 8920, percentage: 71.6 },
            { role: 'contractor', count: 2340, percentage: 18.8 },
            { role: 'broker', count: 1190, percentage: 9.6 },
          ],
          monthlyUserGrowth: [],
        },
        transactions: {
          totalTransactions: 8450,
          successfulTransactions: 8120,
          failedTransactions: 330,
          pendingTransactions: 45,
          totalTransactionValue: 15600000,
          averageTransactionValue: 1846,
          transactionGrowthRate: 18.3,
          transactionsByType: [],
          monthlyTransactionData: [],
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  };

  if (isLoading) {
    return <Loading />;
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Dashboard</h3>
        <p className="text-gray-600 mb-6">Unable to load dashboard data. Please try again.</p>
        <Button onClick={loadDashboardData}>Retry</Button>
      </div>
    );
  }

  const { stats, revenue, userGrowth, transactions } = dashboardData;

  const statCards = [
    {
      title: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      subtitle: `${stats.activeUsers.toLocaleString()} active`,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: userGrowth.userGrowthRate,
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      subtitle: `${formatCurrency(stats.monthlyRevenue)} this month`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: revenue.revenueGrowth,
    },
    {
      title: 'Pending Verifications',
      value: stats.pendingVerifications,
      subtitle: 'Require attention',
      icon: Shield,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      trend: -8.2,
    },
    {
      title: 'Active Projects',
      value: stats.activeProjects.toLocaleString(),
      subtitle: `${stats.totalProjects.toLocaleString()} total`,
      icon: Briefcase,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      trend: 12.8,
    },
  ];

  const quickActions = [
    {
      title: 'User Management',
      description: 'Manage users, roles, and permissions',
      icon: Users,
      href: '/admin/users',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Verifications',
      description: 'Review broker and contractor applications',
      icon: Shield,
      href: '/admin/verifications',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      badge: stats.pendingVerifications > 0 ? stats.pendingVerifications : undefined,
    },
    {
      title: 'Properties',
      description: 'Manage site listings and properties',
      icon: Home,
      href: '/admin/properties',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Analytics',
      description: 'View detailed platform analytics',
      icon: BarChart3,
      href: '/admin/analytics',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Transactions',
      description: 'Monitor payments and transactions',
      icon: DollarSign,
      href: '/admin/transactions',
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
    },
    {
      title: 'System Settings',
      description: 'Configure platform settings',
      icon: Settings,
      href: '/admin/settings',
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
    },
  ];

  const recentActivities = [
    {
      id: '1',
      type: 'verification',
      description: 'New contractor application submitted',
      user: 'Rajesh Kumar',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: 'pending',
    },
    {
      id: '2',
      type: 'user',
      description: 'New user registration',
      user: 'Priya Sharma',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      status: 'success',
    },
    {
      id: '3',
      type: 'property',
      description: 'New site listing created',
      user: 'Amit Patel',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      status: 'success',
    },
    {
      id: '4',
      type: 'transaction',
      description: 'Payment processed successfully',
      user: 'Tech Solutions Pvt Ltd',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      status: 'success',
    },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'verification': return Shield;
      case 'user': return Users;
      case 'property': return Home;
      case 'transaction': return DollarSign;
      default: return Activity;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'pending': return 'text-yellow-600 bg-yellow-50';
      case 'failed': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* System Health Banner */}
      <Card className={`border-2 ${stats.systemHealth === 'healthy' ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${stats.systemHealth === 'healthy' ? 'bg-green-100' : 'bg-yellow-100'}`}>
                {stats.systemHealth === 'healthy' ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                )}
              </div>
              <div>
                <h3 className={`font-medium ${stats.systemHealth === 'healthy' ? 'text-green-900' : 'text-yellow-900'}`}>
                  System Status: {stats.systemHealth === 'healthy' ? 'All Systems Operational' : 'Minor Issues Detected'}
                </h3>
                <p className={`text-sm ${stats.systemHealth === 'healthy' ? 'text-green-800' : 'text-yellow-800'}`}>
                  Platform is running smoothly with no critical issues
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-gray-500">{stat.subtitle}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <IconComponent className={`w-6 h-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <TrendingUp className={`w-4 h-4 mr-1 ${stat.trend > 0 ? 'text-green-600' : 'text-red-600'}`} />
                  <span className={`text-sm font-medium ${stat.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stat.trend > 0 ? '+' : ''}{stat.trend}%
                  </span>
                  <span className="text-sm text-gray-500 ml-1">vs last month</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickActions.map((action, index) => {
              const IconComponent = action.icon;
              return (
                <Link key={index} href={action.href}>
                  <div className="relative p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                    <div className={`w-10 h-10 rounded-full ${action.bgColor} flex items-center justify-center mb-3`}>
                      <IconComponent className={`w-5 h-5 ${action.color}`} />
                    </div>
                    <h3 className="font-medium text-gray-900 mb-1">{action.title}</h3>
                    <p className="text-sm text-gray-600">{action.description}</p>
                    {action.badge && (
                      <div className="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                        {action.badge}
                      </div>
                    )}
                  </div>
                </Link>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Platform Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Platform Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">User Growth Rate</span>
              <span className="font-medium">{userGrowth.userGrowthRate}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${userGrowth.userGrowthRate}%` }}
              ></div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Revenue Growth</span>
              <span className="font-medium">{revenue.revenueGrowth}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${revenue.revenueGrowth}%` }}
              ></div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Transaction Success Rate</span>
              <span className="font-medium">{((transactions.successfulTransactions / transactions.totalTransactions) * 100).toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-purple-600 h-2 rounded-full" 
                style={{ width: `${(transactions.successfulTransactions / transactions.totalTransactions) * 100}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => {
                const ActivityIcon = getActivityIcon(activity.type);
                return (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${getStatusColor(activity.status)}`}>
                      <ActivityIcon className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">
                        by {activity.user} • {formatRelativeTime(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
