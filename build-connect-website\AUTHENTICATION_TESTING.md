# Authentication Testing Guide

This guide explains how to test the signup and login functionality with real backend integration for the BUILD-CONNECT platform.

## Overview

The authentication testing system includes:

- **Test Backend Server**: A complete Express.js server that simulates a real backend API
- **Authentication Tests**: Comprehensive tests for signup, login, and token validation
- **Frontend Integration**: Tests the Next.js API routes that proxy to the backend
- **Error Handling**: Tests various error scenarios and validation

## Quick Start

### Prerequisites

1. **Node.js Dependencies**: Install the required testing dependencies
   ```bash
   npm install
   ```

2. **Frontend Server**: Start the Next.js development server
   ```bash
   npm run dev
   ```
   The frontend should be running on `http://localhost:3000`

### Running the Tests

#### Option 1: Automated Test Runner (Recommended)

Run the complete authentication test suite:

```bash
node run-auth-tests.js
```

This will:
- Check if the frontend is running
- Start the test backend server (if not already running)
- Run all authentication tests
- Clean up automatically

#### Option 2: Manual Testing

1. **Start the test backend server**:
   ```bash
   npm run test:backend
   ```
   The backend will run on `http://localhost:8000`

2. **In another terminal, run the authentication tests**:
   ```bash
   npm run test:auth
   ```

#### Option 3: Using npm scripts

```bash
# Run backend and tests together (requires concurrently)
npm run test:auth-full
```

## Test Coverage

### 1. Backend Health Checks
- ✅ Test backend server connectivity
- ✅ Frontend server connectivity
- ✅ API endpoint availability

### 2. User Registration Tests
- ✅ Direct backend registration
- ✅ Frontend API registration
- ✅ Multiple user roles (buyer, broker, contractor)
- ✅ Password hashing verification
- ✅ Duplicate email handling

### 3. User Login Tests
- ✅ Direct backend login
- ✅ Frontend API login
- ✅ JWT token generation
- ✅ Token validation
- ✅ Remember me functionality

### 4. Error Handling Tests
- ✅ Invalid credentials
- ✅ Missing required fields
- ✅ Invalid email format
- ✅ Weak passwords
- ✅ Duplicate registrations

### 5. Security Tests
- ✅ Password hashing (bcrypt)
- ✅ JWT token validation
- ✅ Token expiration
- ✅ Secure token storage

## Test Data

The tests use the following test users:

```javascript
const testUsers = [
  {
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'password123',
    role: 'buyer',
    phone: '+91-**********'
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: 'securepass456',
    role: 'broker',
    phone: '+91-**********'
  },
  {
    name: 'Bob Wilson',
    email: '<EMAIL>',
    password: 'mypassword789',
    role: 'contractor',
    phone: '+91-**********'
  }
]
```

## API Endpoints Tested

### Backend Endpoints (Port 8000)

- `GET /health` - Health check
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user (requires token)
- `POST /auth/logout` - User logout
- `GET /test/users` - List all users (testing only)
- `DELETE /test/users` - Clear all users (testing only)

### Frontend API Routes (Port 3000)

- `POST /api/auth/register` - Registration proxy
- `POST /api/auth/login` - Login proxy

## Expected Test Results

When all tests pass, you should see output similar to:

```
🚀 Starting Authentication Tests
────────────────────────────────────────────────────────────
ℹ️ Testing backend server health...
✅ Backend server is healthy
ℹ️ Testing frontend server health...
✅ Frontend server is healthy
────────────────────────────────────────────────────────────
ℹ️ Testing Direct Backend Operations
────────────────────────────────────────────────────────────
🧪 Testing direct backend <NAME_EMAIL>...
✅ Direct backend registration <NAME_EMAIL>
🧪 Testing direct backend <NAME_EMAIL>...
✅ Direct backend registration <NAME_EMAIL>
🧪 Testing direct backend <NAME_EMAIL>...
✅ Direct backend registration <NAME_EMAIL>
🧪 Testing direct backend <NAME_EMAIL>...
✅ Direct backend login <NAME_EMAIL>
🧪 Testing token validation...
✅ Token validation successful
...
────────────────────────────────────────────────────────────
📊 Test Results
────────────────────────────────────────────────────────────
ℹ️ Total Tests: 20
✅ Passed: 20
❌ Failed: 0
ℹ️ Success Rate: 100.0%
✅ 🎉 All tests passed! Authentication is working correctly.
────────────────────────────────────────────────────────────
```

## Troubleshooting

### Common Issues

#### 1. Frontend Not Running
```
⚠️ Port 3000 is not in use. Please start your Next.js frontend first:
   npm run dev
```
**Solution**: Start the frontend server with `npm run dev`

#### 2. Port Already in Use
```
⚠️ Port 8000 is already in use
```
**Solution**: Either stop the existing service on port 8000 or the test will use the existing backend

#### 3. Connection Refused
```
❌ Backend server is not responding: connect ECONNREFUSED
```
**Solution**: Check if the backend server started correctly and is listening on the correct port

#### 4. Test Failures
```
❌ Frontend registration error: Request failed with status code 500
```
**Solution**: Check the frontend and backend logs for detailed error messages

### Debug Mode

For detailed debugging, you can:

1. **Check backend logs**: The test backend server shows detailed request/response logs
2. **Check frontend logs**: Look at the Next.js console for API route errors
3. **Manual API testing**: Use tools like Postman or curl to test endpoints directly

### Manual API Testing

You can test the endpoints manually:

```bash
# Health check
curl http://localhost:8000/health

# Register a user
curl -X POST http://localhost:8000/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "buyer"
  }'

# Login
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Get current user (replace TOKEN with actual token)
curl http://localhost:8000/auth/me \
  -H "Authorization: Bearer TOKEN"
```

## Integration with Real Backend

To integrate with a real backend:

1. **Update Environment Variables**:
   ```bash
   NEXT_PUBLIC_API_URL=https://your-backend-api.com/api
   BACKEND_API_URL=https://your-backend-api.com
   BACKEND_API_KEY=your-actual-api-key
   ```

2. **Update API Client Configuration**:
   The API client in `src/lib/api-client.ts` will automatically use the configured URLs

3. **Authentication Service**:
   The authentication service in `src/services/auth.service.ts` is ready for production use

4. **Frontend API Routes**:
   The Next.js API routes in `src/app/api/auth/` will proxy to your real backend

## Security Considerations

The test backend includes several security features that should be present in your real backend:

- ✅ Password hashing with bcrypt
- ✅ JWT token generation and validation
- ✅ Input validation and sanitization
- ✅ Error handling without information leakage
- ✅ Rate limiting (basic implementation)
- ✅ CORS configuration

## Next Steps

After successful authentication testing:

1. **Deploy Backend**: Deploy your real backend API
2. **Update Configuration**: Update environment variables for production
3. **Add More Features**: Implement additional authentication features like:
   - Email verification
   - Password reset
   - Two-factor authentication
   - Social login
4. **Add Monitoring**: Implement logging and monitoring for authentication events

## Support

If you encounter issues with authentication testing:

1. Check the [Backend Integration Guide](./BACKEND_INTEGRATION.md)
2. Review the API service implementations in `src/services/`
3. Check the React Query hooks in `src/hooks/api/`
4. Examine the Next.js API routes in `src/app/api/auth/`

---

This authentication testing system provides a comprehensive way to verify that your signup and login functionality works correctly with real backend integration.
