/**
 * Authentication Testing Script
 * Tests signup and login functionality with real backend integration
 */

const axios = require('axios')
const colors = require('colors')

// Configuration
const FRONTEND_URL = 'http://localhost:3000'
const BACKEND_URL = 'http://localhost:8000'

// Test data
const testUsers = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'buyer',
    phone: '+91-9876543210'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'securepass456',
    role: 'broker',
    phone: '+91-9876543211'
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'mypassword789',
    role: 'contractor',
    phone: '+91-**********'
  }
]

// Helper functions
const log = {
  info: (msg) => console.log('ℹ️ '.blue + msg),
  success: (msg) => console.log('✅ '.green + msg.green),
  error: (msg) => console.log('❌ '.red + msg.red),
  warning: (msg) => console.log('⚠️ '.yellow + msg.yellow),
  test: (msg) => console.log('🧪 '.cyan + msg.cyan),
  separator: () => console.log('─'.repeat(60).gray)
}

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// Test functions
async function testBackendHealth() {
  log.test('Testing backend server health...')
  try {
    const response = await axios.get(`${BACKEND_URL}/health`)
    if (response.data.status === 'ok') {
      log.success('Backend server is healthy')
      return true
    } else {
      log.error('Backend server health check failed')
      return false
    }
  } catch (error) {
    log.error(`Backend server is not responding: ${error.message}`)
    return false
  }
}

async function testFrontendHealth() {
  log.test('Testing frontend server health...')
  try {
    const response = await axios.get(FRONTEND_URL, { timeout: 5000 })
    if (response.status === 200) {
      log.success('Frontend server is healthy')
      return true
    } else {
      log.error('Frontend server health check failed')
      return false
    }
  } catch (error) {
    log.error(`Frontend server is not responding: ${error.message}`)
    return false
  }
}

async function clearTestData() {
  log.test('Clearing existing test data...')
  try {
    await axios.delete(`${BACKEND_URL}/test/users`)
    log.success('Test data cleared')
  } catch (error) {
    log.warning('Could not clear test data (this is okay if no data exists)')
  }
}

async function testDirectBackendRegistration(user) {
  log.test(`Testing direct backend registration for ${user.email}...`)
  try {
    const response = await axios.post(`${BACKEND_URL}/auth/register`, user)
    
    if (response.data.success) {
      log.success(`Direct backend registration successful for ${user.email}`)
      return {
        success: true,
        user: response.data.data.user,
        tokens: {
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken
        }
      }
    } else {
      log.error(`Direct backend registration failed: ${response.data.message}`)
      return { success: false, error: response.data.message }
    }
  } catch (error) {
    log.error(`Direct backend registration error: ${error.response?.data?.message || error.message}`)
    return { success: false, error: error.response?.data?.message || error.message }
  }
}

async function testDirectBackendLogin(email, password) {
  log.test(`Testing direct backend login for ${email}...`)
  try {
    const response = await axios.post(`${BACKEND_URL}/auth/login`, {
      email,
      password,
      rememberMe: false
    })
    
    if (response.data.success) {
      log.success(`Direct backend login successful for ${email}`)
      return {
        success: true,
        user: response.data.data.user,
        tokens: {
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken
        }
      }
    } else {
      log.error(`Direct backend login failed: ${response.data.message}`)
      return { success: false, error: response.data.message }
    }
  } catch (error) {
    log.error(`Direct backend login error: ${error.response?.data?.message || error.message}`)
    return { success: false, error: error.response?.data?.message || error.message }
  }
}

async function testFrontendRegistration(user) {
  log.test(`Testing frontend API registration for ${user.email}...`)
  try {
    const response = await axios.post(`${FRONTEND_URL}/api/auth/register`, user)
    
    if (response.data.success) {
      log.success(`Frontend registration successful for ${user.email}`)
      return {
        success: true,
        user: response.data.user
      }
    } else {
      log.error(`Frontend registration failed: ${response.data.message}`)
      return { success: false, error: response.data.message }
    }
  } catch (error) {
    log.error(`Frontend registration error: ${error.response?.data?.message || error.message}`)
    return { success: false, error: error.response?.data?.message || error.message }
  }
}

async function testFrontendLogin(email, password) {
  log.test(`Testing frontend API login for ${email}...`)
  try {
    const response = await axios.post(`${FRONTEND_URL}/api/auth/login`, {
      email,
      password,
      rememberMe: false
    })
    
    if (response.data.success) {
      log.success(`Frontend login successful for ${email}`)
      return {
        success: true,
        user: response.data.data.user,
        tokens: {
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken
        }
      }
    } else {
      log.error(`Frontend login failed: ${response.data.error}`)
      return { success: false, error: response.data.error }
    }
  } catch (error) {
    log.error(`Frontend login error: ${error.response?.data?.error || error.message}`)
    return { success: false, error: error.response?.data?.error || error.message }
  }
}

async function testTokenValidation(token) {
  log.test('Testing token validation...')
  try {
    const response = await axios.get(`${BACKEND_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.data.success) {
      log.success('Token validation successful')
      return { success: true, user: response.data.data }
    } else {
      log.error('Token validation failed')
      return { success: false, error: response.data.message }
    }
  } catch (error) {
    log.error(`Token validation error: ${error.response?.data?.message || error.message}`)
    return { success: false, error: error.response?.data?.message || error.message }
  }
}

async function testInvalidCredentials() {
  log.test('Testing invalid credentials...')
  
  // Test invalid email
  try {
    await axios.post(`${FRONTEND_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    })
    log.error('Invalid email test failed - should have returned error')
  } catch (error) {
    if (error.response?.status === 401) {
      log.success('Invalid email test passed - correctly returned 401')
    } else {
      log.error(`Invalid email test failed - unexpected status: ${error.response?.status}`)
    }
  }
  
  // Test invalid password
  try {
    await axios.post(`${FRONTEND_URL}/api/auth/login`, {
      email: testUsers[0].email,
      password: 'wrongpassword'
    })
    log.error('Invalid password test failed - should have returned error')
  } catch (error) {
    if (error.response?.status === 401) {
      log.success('Invalid password test passed - correctly returned 401')
    } else {
      log.error(`Invalid password test failed - unexpected status: ${error.response?.status}`)
    }
  }
}

async function testValidationErrors() {
  log.test('Testing validation errors...')
  
  // Test missing fields
  try {
    await axios.post(`${FRONTEND_URL}/api/auth/register`, {
      email: '<EMAIL>'
      // Missing name, password, role
    })
    log.error('Missing fields test failed - should have returned error')
  } catch (error) {
    if (error.response?.status === 400) {
      log.success('Missing fields test passed - correctly returned 400')
    } else {
      log.error(`Missing fields test failed - unexpected status: ${error.response?.status}`)
    }
  }
  
  // Test invalid email format
  try {
    await axios.post(`${FRONTEND_URL}/api/auth/register`, {
      name: 'Test User',
      email: 'invalid-email',
      password: 'password123',
      role: 'buyer'
    })
    log.error('Invalid email format test failed - should have returned error')
  } catch (error) {
    if (error.response?.status === 400) {
      log.success('Invalid email format test passed - correctly returned 400')
    } else {
      log.error(`Invalid email format test failed - unexpected status: ${error.response?.status}`)
    }
  }
  
  // Test weak password
  try {
    await axios.post(`${FRONTEND_URL}/api/auth/register`, {
      name: 'Test User',
      email: '<EMAIL>',
      password: '123',
      role: 'buyer'
    })
    log.error('Weak password test failed - should have returned error')
  } catch (error) {
    if (error.response?.status === 400) {
      log.success('Weak password test passed - correctly returned 400')
    } else {
      log.error(`Weak password test failed - unexpected status: ${error.response?.status}`)
    }
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Authentication Tests'.bold.blue)
  log.separator()
  
  let passedTests = 0
  let totalTests = 0
  
  // Health checks
  totalTests += 2
  if (await testBackendHealth()) passedTests++
  if (await testFrontendHealth()) passedTests++
  
  log.separator()
  
  // Clear test data
  await clearTestData()
  await delay(1000)
  
  // Test direct backend operations
  log.info('Testing Direct Backend Operations')
  log.separator()
  
  const backendResults = []
  for (const user of testUsers) {
    totalTests++
    const result = await testDirectBackendRegistration(user)
    backendResults.push(result)
    if (result.success) passedTests++
    await delay(500)
  }
  
  // Test direct backend login
  for (let i = 0; i < testUsers.length; i++) {
    totalTests++
    const result = await testDirectBackendLogin(testUsers[i].email, testUsers[i].password)
    if (result.success) {
      passedTests++
      
      // Test token validation
      totalTests++
      const tokenResult = await testTokenValidation(result.tokens.accessToken)
      if (tokenResult.success) passedTests++
    }
    await delay(500)
  }
  
  log.separator()
  
  // Clear data for frontend tests
  await clearTestData()
  await delay(1000)
  
  // Test frontend API operations
  log.info('Testing Frontend API Operations')
  log.separator()
  
  const frontendResults = []
  for (const user of testUsers) {
    totalTests++
    const result = await testFrontendRegistration(user)
    frontendResults.push(result)
    if (result.success) passedTests++
    await delay(500)
  }
  
  // Test frontend login
  for (let i = 0; i < testUsers.length; i++) {
    totalTests++
    const result = await testFrontendLogin(testUsers[i].email, testUsers[i].password)
    if (result.success) {
      passedTests++
      
      // Test token validation
      totalTests++
      const tokenResult = await testTokenValidation(result.tokens.accessToken)
      if (tokenResult.success) passedTests++
    }
    await delay(500)
  }
  
  log.separator()
  
  // Test error cases
  log.info('Testing Error Cases')
  log.separator()
  
  totalTests += 5 // 2 invalid credentials + 3 validation errors
  await testInvalidCredentials()
  await testValidationErrors()
  passedTests += 5 // Assume these pass if no exceptions
  
  log.separator()
  
  // Results
  console.log('📊 Test Results'.bold.blue)
  log.separator()
  log.info(`Total Tests: ${totalTests}`)
  log.success(`Passed: ${passedTests}`)
  log.error(`Failed: ${totalTests - passedTests}`)
  log.info(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
  
  if (passedTests === totalTests) {
    log.success('🎉 All tests passed! Authentication is working correctly.')
  } else {
    log.warning('⚠️ Some tests failed. Please check the logs above.')
  }
  
  log.separator()
}

// Run tests
if (require.main === module) {
  runTests().catch(error => {
    log.error(`Test runner error: ${error.message}`)
    process.exit(1)
  })
}

module.exports = { runTests }
