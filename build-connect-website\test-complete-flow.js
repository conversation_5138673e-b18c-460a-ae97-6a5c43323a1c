/**
 * Complete Authentication and Brokers Flow Test
 * Tests the complete flow from login to viewing brokers
 */

const fetch = globalThis.fetch || require('node-fetch');

const BACKEND_URL = 'http://localhost:8080';
const FRONTEND_URL = 'http://localhost:3008';

// Test user
const testUser = {
  name: 'Test User',
  email: `user-${Date.now()}@example.com`,
  password: 'TestPassword123!',
  role: 'buyer',
  phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
};

async function testCompleteAuthFlow() {
  console.log('\n🔐 Testing Complete Authentication Flow...');
  
  try {
    // Step 1: Register user
    console.log('📝 Step 1: Registering user');
    const registerResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });

    if (!registerResponse.ok) {
      const error = await registerResponse.text();
      console.log(`   ❌ Registration failed: ${error}`);
      return { success: false };
    }

    console.log('   ✅ User registered successfully');

    // Step 2: Login user
    console.log('🔑 Step 2: Logging in user');
    const loginResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });

    if (!loginResponse.ok) {
      const error = await loginResponse.text();
      console.log(`   ❌ Login failed: ${error}`);
      return { success: false };
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('   ✅ Login successful');
    console.log(`   📄 Token: ${token ? 'Present' : 'Missing'}`);

    // Step 3: Test authenticated broker listing
    console.log('📋 Step 3: Testing authenticated broker listing');
    const brokersResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/brokers/all`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`   Status: ${brokersResponse.status} ${brokersResponse.statusText}`);
    
    if (brokersResponse.ok) {
      const brokersData = await brokersResponse.json();
      console.log('   ✅ Authenticated broker listing successful');
      console.log(`   📄 Response type: ${Array.isArray(brokersData) ? 'Array' : 'Object'}`);
      
      const brokers = brokersData.brokers || brokersData;
      if (Array.isArray(brokers)) {
        console.log(`   📄 Total brokers: ${brokers.length}`);
      } else {
        console.log(`   📄 Response structure: ${Object.keys(brokersData).join(', ')}`);
      }
      
      return { success: true, token, brokers: brokersData };
    } else {
      const error = await brokersResponse.text();
      console.log(`   ❌ Authenticated broker listing failed: ${error}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Complete auth flow test failed: ${error.message}`);
    return { success: false };
  }
}

async function testFrontendAuthFlow() {
  console.log('\n🌐 Testing Frontend Authentication Flow...');
  
  try {
    // Test frontend login API
    console.log('🔑 Testing frontend login API');
    const frontendLoginResponse = await fetch(`${FRONTEND_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });

    console.log(`   Status: ${frontendLoginResponse.status} ${frontendLoginResponse.statusText}`);
    
    if (frontendLoginResponse.ok) {
      const frontendLoginData = await frontendLoginResponse.json();
      console.log('   ✅ Frontend login successful');
      console.log(`   📄 Success: ${frontendLoginData.success}`);
      
      // Check for cookies
      const cookies = frontendLoginResponse.headers.get('set-cookie');
      if (cookies) {
        console.log('   🍪 Cookies set successfully');
      }
      
      return { success: true, data: frontendLoginData, cookies };
    } else {
      const error = await frontendLoginResponse.text();
      console.log(`   ❌ Frontend login failed: ${error}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Frontend auth flow test failed: ${error.message}`);
    return { success: false };
  }
}

async function testNextAuthSession() {
  console.log('\n👤 Testing NextAuth Session...');
  
  try {
    // Test session endpoint
    const sessionResponse = await fetch(`${FRONTEND_URL}/api/auth/session`);
    
    if (sessionResponse.ok) {
      const session = await sessionResponse.json();
      console.log('   ✅ Session endpoint working');
      console.log(`   📄 Session: ${session ? JSON.stringify(session).substring(0, 100) : 'null'}`);
      return { success: true, session };
    } else {
      console.log('   ❌ Session endpoint failed');
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ NextAuth session test failed: ${error.message}`);
    return { success: false };
  }
}

async function testBrokerEndpoints() {
  console.log('\n🏢 Testing All Broker Endpoints...');
  
  try {
    // First, let's check what endpoints are available
    const endpoints = [
      '/user-service/api/v1/brokers/all',
      '/user-service/api/v1/professionals/brokers',
      '/user-service/health',
      '/user-service/api/v1'
    ];

    for (const endpoint of endpoints) {
      console.log(`🔍 Testing: ${endpoint}`);
      try {
        const response = await fetch(`${BACKEND_URL}${endpoint}`);
        console.log(`   Status: ${response.status} ${response.statusText}`);
        
        if (response.ok) {
          const data = await response.text();
          console.log(`   📄 Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`);
        } else {
          const error = await response.text();
          console.log(`   📄 Error: ${error.substring(0, 100)}${error.length > 100 ? '...' : ''}`);
        }
      } catch (error) {
        console.log(`   ❌ Connection error: ${error.message}`);
      }
    }
    
    return { success: true };
  } catch (error) {
    console.log(`   ❌ Broker endpoints test failed: ${error.message}`);
    return { success: false };
  }
}

async function main() {
  console.log('🚀 Build Connect Complete Flow Test');
  console.log('====================================');
  console.log(`🔧 Backend: ${BACKEND_URL}`);
  console.log(`🌐 Frontend: ${FRONTEND_URL}`);
  console.log(`👤 Test User: ${testUser.email}`);
  
  // Test complete authentication flow
  const authResult = await testCompleteAuthFlow();
  
  // Test frontend authentication
  const frontendAuthResult = await testFrontendAuthFlow();
  
  // Test NextAuth session
  const sessionResult = await testNextAuthSession();
  
  // Test broker endpoints
  const brokerEndpointsResult = await testBrokerEndpoints();
  
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`🔐 Backend Auth Flow: ${authResult.success ? '✅ Working' : '❌ Failed'}`);
  console.log(`🌐 Frontend Auth Flow: ${frontendAuthResult.success ? '✅ Working' : '❌ Failed'}`);
  console.log(`👤 NextAuth Session: ${sessionResult.success ? '✅ Working' : '❌ Failed'}`);
  console.log(`🏢 Broker Endpoints: ${brokerEndpointsResult.success ? '✅ Working' : '❌ Failed'}`);
  
  if (authResult.success) {
    console.log('\n🎉 Backend authentication is working!');
    console.log('💡 Key findings:');
    console.log('   ✅ User registration works');
    console.log('   ✅ User login works');
    console.log('   ✅ Authenticated API calls work');
    console.log('   ✅ JWT tokens are being issued correctly');
  }
  
  if (frontendAuthResult.success) {
    console.log('\n🎉 Frontend authentication is working!');
    console.log('💡 The frontend can successfully authenticate with the backend');
  }
  
  console.log('\n🔄 Next Steps:');
  console.log('   1. Test login in browser: http://localhost:3008/auth/login');
  console.log('   2. After login, test brokers page: http://localhost:3008/brokers');
  console.log('   3. Check browser developer tools for any errors');
  console.log('   4. Test broker registration flow');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', reason);
});

main().catch(console.error);
