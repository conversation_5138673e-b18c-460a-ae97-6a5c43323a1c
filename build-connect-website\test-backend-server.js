/**
 * Test Backend Server for Authentication Testing
 * This simulates a real backend API for testing purposes
 */

const express = require('express')
const cors = require('cors')
const jwt = require('jsonwebtoken')
const bcrypt = require('bcryptjs')

const app = express()
const PORT = process.env.TEST_BACKEND_PORT || 8000

// Middleware
app.use(cors())
app.use(express.json())

// In-memory user storage (for testing only)
const users = new Map()
const JWT_SECRET = 'test-jwt-secret-key'

// Helper functions
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    { 
      id: user.id, 
      email: user.email, 
      role: user.role 
    },
    JWT_SECRET,
    { expiresIn: '1h' }
  )
  
  const refreshToken = jwt.sign(
    { 
      id: user.id, 
      type: 'refresh' 
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  )
  
  return { accessToken, refreshToken }
}

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePassword = (password) => {
  return password && password.length >= 8
}

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'Test backend server is running',
    timestamp: new Date().toISOString()
  })
})

// Register endpoint
app.post('/auth/register', async (req, res) => {
  try {
    const { name, email, password, role, phone } = req.body

    // Validation
    if (!name || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        errors: {
          name: !name ? ['Name is required'] : [],
          email: !email ? ['Email is required'] : [],
          password: !password ? ['Password is required'] : [],
          role: !role ? ['Role is required'] : []
        }
      })
    }

    if (!validateEmail(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format',
        errors: {
          email: ['Please enter a valid email address']
        }
      })
    }

    if (!validatePassword(password)) {
      return res.status(400).json({
        success: false,
        message: 'Password must be at least 8 characters long',
        errors: {
          password: ['Password must be at least 8 characters long']
        }
      })
    }

    const validRoles = ['buyer', 'broker', 'contractor', 'admin']
    if (!validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role',
        errors: {
          role: ['Role must be one of: buyer, broker, contractor, admin']
        }
      })
    }

    // Check if user already exists
    if (users.has(email)) {
      return res.status(409).json({
        success: false,
        message: 'User already exists',
        errors: {
          email: ['An account with this email already exists']
        }
      })
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12)

    // Create user
    const user = {
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      email,
      password: hashedPassword,
      role,
      phone: phone || null,
      isVerified: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // Store user
    users.set(email, user)

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user)

    // Return response (exclude password)
    const { password: _, ...userResponse } = user

    res.status(201).json({
      success: true,
      message: 'Registration successful',
      data: {
        user: userResponse,
        accessToken,
        refreshToken,
        expiresIn: 3600
      }
    })

  } catch (error) {
    console.error('Registration error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    })
  }
})

// Login endpoint
app.post('/auth/login', async (req, res) => {
  try {
    const { email, password, rememberMe } = req.body

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required',
        errors: {
          email: !email ? ['Email is required'] : [],
          password: !password ? ['Password is required'] : []
        }
      })
    }

    if (!validateEmail(email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format',
        errors: {
          email: ['Please enter a valid email address']
        }
      })
    }

    // Find user
    const user = users.get(email)
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        errors: {
          email: ['No account found with this email address']
        }
      })
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        errors: {
          password: ['Incorrect password']
        }
      })
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user)

    // Update last login
    user.lastLoginAt = new Date().toISOString()
    users.set(email, user)

    // Return response (exclude password)
    const { password: _, ...userResponse } = user

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        accessToken,
        refreshToken,
        expiresIn: rememberMe ? 7 * 24 * 3600 : 3600 // 7 days or 1 hour
      }
    })

  } catch (error) {
    console.error('Login error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    })
  }
})

// Get current user endpoint
app.get('/auth/me', (req, res) => {
  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      })
    }

    const token = authHeader.substring(7)
    const decoded = jwt.verify(token, JWT_SECRET)
    
    const user = Array.from(users.values()).find(u => u.id === decoded.id)
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      })
    }

    const { password: _, ...userResponse } = user

    res.json({
      success: true,
      data: userResponse
    })

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      })
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      })
    }

    console.error('Auth verification error:', error)
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    })
  }
})

// Logout endpoint
app.post('/auth/logout', (req, res) => {
  // In a real implementation, you would invalidate the token
  res.json({
    success: true,
    message: 'Logout successful'
  })
})

// Get all users (for testing purposes)
app.get('/test/users', (req, res) => {
  const userList = Array.from(users.values()).map(user => {
    const { password: _, ...userResponse } = user
    return userResponse
  })
  
  res.json({
    success: true,
    data: userList,
    total: userList.length
  })
})

// Clear all users (for testing purposes)
app.delete('/test/users', (req, res) => {
  users.clear()
  res.json({
    success: true,
    message: 'All users cleared'
  })
})

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error)
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: error.message
  })
})

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test Backend Server running on http://localhost:${PORT}`)
  console.log(`📋 Health check: http://localhost:${PORT}/health`)
  console.log(`👥 Test users: http://localhost:${PORT}/test/users`)
  console.log('')
  console.log('Available endpoints:')
  console.log('  POST /auth/register - User registration')
  console.log('  POST /auth/login - User login')
  console.log('  GET  /auth/me - Get current user')
  console.log('  POST /auth/logout - User logout')
  console.log('  GET  /test/users - List all users (testing)')
  console.log('  DELETE /test/users - Clear all users (testing)')
})

module.exports = app
