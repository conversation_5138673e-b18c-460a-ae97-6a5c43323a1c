/**
 * Brokers Integration Test
 * Tests the brokers functionality with the backend
 */

const fetch = globalThis.fetch || require('node-fetch');

const BACKEND_URL = 'http://localhost:8080';
const FRONTEND_URL = 'http://localhost:3008';

// Test user for broker application
const testBrokerUser = {
  name: 'Test Broker',
  email: `broker-${Date.now()}@example.com`,
  password: 'TestPassword123!',
  role: 'broker',
  phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
};

async function testBrokerRegistration() {
  console.log('\n🏢 Testing Broker Registration...');
  
  try {
    // First register as a user
    console.log('📝 Registering broker user');
    const userResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBrokerUser)
    });

    if (!userResponse.ok) {
      const error = await userResponse.text();
      console.log(`   ❌ User registration failed: ${error}`);
      return { success: false };
    }

    const userData = await userResponse.json();
    console.log('   ✅ User registered successfully');
    
    // Login to get token
    console.log('🔑 Logging in broker user');
    const loginResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testBrokerUser.email,
        password: testBrokerUser.password
      })
    });

    if (!loginResponse.ok) {
      console.log('   ❌ Login failed');
      return { success: false };
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('   ✅ Login successful');

    // Test broker application
    console.log('📋 Testing broker application');
    const brokerApplicationData = {
      personalInfo: {
        fullName: testBrokerUser.name,
        email: testBrokerUser.email,
        phone: testBrokerUser.phone,
        address: {
          street: '123 Test Street',
          city: 'Bangalore',
          state: 'Karnataka',
          pincode: '560001'
        }
      },
      businessInfo: {
        companyName: 'Test Realty',
        yearsOfExperience: 5,
        specializations: ['residential', 'commercial'],
        serviceAreas: ['Bangalore', 'Mysore'],
        licenseNumber: 'RERA123456',
        gstNumber: 'GST123456789'
      }
    };

    const brokerResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/brokers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(brokerApplicationData)
    });

    console.log(`   Status: ${brokerResponse.status} ${brokerResponse.statusText}`);
    
    if (brokerResponse.ok) {
      const brokerData = await brokerResponse.json();
      console.log('   ✅ Broker application submitted successfully');
      console.log(`   📄 Broker ID: ${brokerData.id || brokerData._id || 'N/A'}`);
      return { success: true, brokerId: brokerData.id || brokerData._id, token };
    } else {
      const error = await brokerResponse.text();
      console.log(`   ❌ Broker application failed: ${error}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Broker registration test failed: ${error.message}`);
    return { success: false };
  }
}

async function testBrokerListing() {
  console.log('\n📋 Testing Broker Listing...');
  
  try {
    // Test getting all brokers
    console.log('📝 Fetching all brokers');
    const brokersResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/brokers/all`);

    console.log(`   Status: ${brokersResponse.status} ${brokersResponse.statusText}`);
    
    if (brokersResponse.ok) {
      const brokersData = await brokersResponse.json();
      console.log('   ✅ Brokers listing successful');
      console.log(`   📄 Total brokers: ${brokersData.length || brokersData.brokers?.length || 'N/A'}`);
      
      // Check if we have any brokers
      const brokers = brokersData.brokers || brokersData;
      if (Array.isArray(brokers) && brokers.length > 0) {
        console.log(`   📄 First broker: ${brokers[0].name || brokers[0].personalInfo?.fullName || 'N/A'}`);
        return { success: true, brokers };
      } else {
        console.log('   ⚠️  No brokers found in the system');
        return { success: true, brokers: [] };
      }
    } else {
      const error = await brokersResponse.text();
      console.log(`   ❌ Brokers listing failed: ${error}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Broker listing test failed: ${error.message}`);
    return { success: false };
  }
}

async function testFrontendBrokerPage() {
  console.log('\n🌐 Testing Frontend Broker Page...');
  
  try {
    // Test if the brokers page loads
    console.log('📝 Testing brokers page load');
    const pageResponse = await fetch(`${FRONTEND_URL}/brokers`);

    console.log(`   Status: ${pageResponse.status} ${pageResponse.statusText}`);
    
    if (pageResponse.ok) {
      const pageContent = await pageResponse.text();
      console.log('   ✅ Brokers page loads successfully');
      
      // Check if the page contains expected content
      if (pageContent.includes('Find Brokers') || pageContent.includes('broker')) {
        console.log('   ✅ Page contains broker-related content');
        return { success: true };
      } else {
        console.log('   ⚠️  Page loaded but may not contain expected content');
        return { success: true };
      }
    } else {
      console.log(`   ❌ Brokers page failed to load: ${pageResponse.statusText}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Frontend broker page test failed: ${error.message}`);
    return { success: false };
  }
}

async function main() {
  console.log('🚀 Build Connect Brokers Integration Test');
  console.log('==========================================');
  console.log(`🔧 Backend: ${BACKEND_URL}`);
  console.log(`🌐 Frontend: ${FRONTEND_URL}`);
  console.log(`👤 Test Broker: ${testBrokerUser.email}`);
  
  // Test broker registration flow
  const registrationResult = await testBrokerRegistration();
  
  // Test broker listing
  const listingResult = await testBrokerListing();
  
  // Test frontend broker page
  const frontendResult = await testFrontendBrokerPage();
  
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`🏢 Broker Registration: ${registrationResult.success ? '✅ Working' : '❌ Failed'}`);
  console.log(`📋 Broker Listing: ${listingResult.success ? '✅ Working' : '❌ Failed'}`);
  console.log(`🌐 Frontend Page: ${frontendResult.success ? '✅ Working' : '❌ Failed'}`);
  
  if (registrationResult.success && listingResult.success && frontendResult.success) {
    console.log('\n🎉 All broker tests passed!');
    console.log('💡 The broker functionality is working end-to-end');
    console.log('💡 You can now:');
    console.log('   1. Register as a broker through the frontend');
    console.log('   2. View brokers on the brokers page');
    console.log('   3. Test broker profile functionality');
  } else {
    console.log('\n⚠️  Some broker tests failed');
    console.log('💡 Check the error messages above for details');
  }
  
  // Provide next steps
  console.log('\n🔄 Next Steps:');
  console.log('   1. Test the brokers page in browser: http://localhost:3008/brokers');
  console.log('   2. Test broker registration: http://localhost:3008/auth/register');
  console.log('   3. Update other components to use new services');
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', reason);
});

main().catch(console.error);
