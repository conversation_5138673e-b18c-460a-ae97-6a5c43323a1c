/**
 * Authentication Flow Test
 * Tests the complete authentication flow from frontend to backend
 */

const fetch = globalThis.fetch || require('node-fetch');

const FRONTEND_URL = 'http://localhost:3008';
const BACKEND_URL = 'http://localhost:8080';

// Test user data
const testUser = {
  name: 'Test User',
  email: `test-${Date.now()}@example.com`,
  password: 'TestPassword123!',
  role: 'buyer',
  phone: `98765${Math.floor(Math.random() * 100000)
    .toString()
    .padStart(5, '0')}`,
};

async function testDirectBackendAuth() {
  console.log('\n🔧 Testing Direct Backend Authentication...');

  try {
    // Test registration directly with backend
    console.log('\n📝 Testing Direct Backend Registration');
    const registerResponse = await fetch(
      `${BACKEND_URL}/user-service/api/v1/signup`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testUser),
      }
    );

    console.log(
      `   Status: ${registerResponse.status} ${registerResponse.statusText}`
    );

    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log('   ✅ Direct backend registration successful');
      console.log(
        `   📄 User ID: ${registerData.user?.id || registerData.user?._id || 'N/A'}`
      );
      console.log(`   📄 Token: ${registerData.token ? 'Present' : 'Missing'}`);

      // Test login with the registered user
      console.log('\n🔑 Testing Direct Backend Login');
      const loginResponse = await fetch(
        `${BACKEND_URL}/user-service/api/v1/login`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: testUser.email,
            password: testUser.password,
          }),
        }
      );

      console.log(
        `   Status: ${loginResponse.status} ${loginResponse.statusText}`
      );

      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log('   ✅ Direct backend login successful');
        console.log(`   📄 User: ${loginData.user?.name || 'N/A'}`);
        console.log(`   📄 Role: ${loginData.user?.role || 'N/A'}`);
        console.log(`   📄 Token: ${loginData.token ? 'Present' : 'Missing'}`);
        return { success: true, token: loginData.token, user: loginData.user };
      } else {
        const loginError = await loginResponse.text();
        console.log(`   ❌ Direct backend login failed: ${loginError}`);
        return { success: false };
      }
    } else {
      const registerError = await registerResponse.text();
      console.log(`   ❌ Direct backend registration failed: ${registerError}`);
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Direct backend test failed: ${error.message}`);
    return { success: false };
  }
}

async function testFrontendAuth() {
  console.log('\n🌐 Testing Frontend Authentication Flow...');

  try {
    // Test frontend registration API route
    console.log('\n📝 Testing Frontend Registration API');
    const frontendRegisterResponse = await fetch(
      `${FRONTEND_URL}/api/auth/register`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...testUser,
          email: `frontend-${Date.now()}@example.com`,
        }),
      }
    );

    console.log(
      `   Status: ${frontendRegisterResponse.status} ${frontendRegisterResponse.statusText}`
    );

    if (frontendRegisterResponse.ok) {
      const frontendRegisterData = await frontendRegisterResponse.json();
      console.log('   ✅ Frontend registration API successful');
      console.log(
        `   📄 Response: ${JSON.stringify(frontendRegisterData).substring(0, 100)}...`
      );
    } else {
      const frontendRegisterError = await frontendRegisterResponse.text();
      console.log(
        `   ❌ Frontend registration API failed: ${frontendRegisterError.substring(0, 200)}`
      );
    }

    // Test frontend login API route
    console.log('\n🔑 Testing Frontend Login API');
    const frontendLoginResponse = await fetch(
      `${FRONTEND_URL}/api/auth/login`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password,
        }),
      }
    );

    console.log(
      `   Status: ${frontendLoginResponse.status} ${frontendLoginResponse.statusText}`
    );

    if (frontendLoginResponse.ok) {
      const frontendLoginData = await frontendLoginResponse.json();
      console.log('   ✅ Frontend login API successful');
      console.log(
        `   📄 Response: ${JSON.stringify(frontendLoginData).substring(0, 100)}...`
      );

      // Check if cookies were set
      const cookies = frontendLoginResponse.headers.get('set-cookie');
      if (cookies) {
        console.log('   🍪 Cookies set successfully');
        console.log(`   📄 Cookies: ${cookies.substring(0, 100)}...`);
      } else {
        console.log('   ⚠️  No cookies were set');
      }

      return { success: true, data: frontendLoginData };
    } else {
      const frontendLoginError = await frontendLoginResponse.text();
      console.log(
        `   ❌ Frontend login API failed: ${frontendLoginError.substring(0, 200)}`
      );
      return { success: false };
    }
  } catch (error) {
    console.log(`   ❌ Frontend test failed: ${error.message}`);
    return { success: false };
  }
}

async function testNextAuthIntegration() {
  console.log('\n🔐 Testing NextAuth Integration...');

  try {
    // Test NextAuth providers endpoint
    console.log('\n🔍 Testing NextAuth Providers');
    const providersResponse = await fetch(`${FRONTEND_URL}/api/auth/providers`);

    if (providersResponse.ok) {
      const providers = await providersResponse.json();
      console.log('   ✅ NextAuth providers endpoint working');
      console.log(
        `   📄 Available providers: ${Object.keys(providers).join(', ')}`
      );
    } else {
      console.log('   ❌ NextAuth providers endpoint failed');
    }

    // Test NextAuth session endpoint
    console.log('\n👤 Testing NextAuth Session');
    const sessionResponse = await fetch(`${FRONTEND_URL}/api/auth/session`);

    if (sessionResponse.ok) {
      const session = await sessionResponse.json();
      console.log('   ✅ NextAuth session endpoint working');
      console.log(
        `   📄 Session: ${session ? 'Active session found' : 'No active session'}`
      );
    } else {
      console.log('   ❌ NextAuth session endpoint failed');
    }

    // Test NextAuth CSRF token
    console.log('\n🛡️  Testing NextAuth CSRF');
    const csrfResponse = await fetch(`${FRONTEND_URL}/api/auth/csrf`);

    if (csrfResponse.ok) {
      const csrf = await csrfResponse.json();
      console.log('   ✅ NextAuth CSRF endpoint working');
      console.log(
        `   📄 CSRF Token: ${csrf.csrfToken ? 'Present' : 'Missing'}`
      );
    } else {
      console.log('   ❌ NextAuth CSRF endpoint failed');
    }
  } catch (error) {
    console.log(`   ❌ NextAuth integration test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Build Connect Authentication Flow Test');
  console.log('==========================================');
  console.log(`🌐 Frontend: ${FRONTEND_URL}`);
  console.log(`🔧 Backend: ${BACKEND_URL}`);
  console.log(`👤 Test User: ${testUser.email}`);

  // Test direct backend authentication
  const backendResult = await testDirectBackendAuth();

  // Test frontend authentication
  const frontendResult = await testFrontendAuth();

  // Test NextAuth integration
  await testNextAuthIntegration();

  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(
    `🔧 Direct Backend Auth: ${backendResult.success ? '✅ Working' : '❌ Failed'}`
  );
  console.log(
    `🌐 Frontend Auth API: ${frontendResult.success ? '✅ Working' : '❌ Failed'}`
  );

  if (backendResult.success && frontendResult.success) {
    console.log('\n🎉 Authentication flow is working end-to-end!');
    console.log('💡 Next steps:');
    console.log('   1. Test the login page in browser');
    console.log('   2. Test role-based redirects');
    console.log('   3. Test token refresh functionality');
    console.log('   4. Update components to use new services');
  } else {
    console.log('\n⚠️  Some authentication tests failed');
    console.log('💡 Check the error messages above for details');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', reason);
});

main().catch(console.error);
