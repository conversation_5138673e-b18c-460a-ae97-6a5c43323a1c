'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ChevronLeft, ChevronRight, MapPin, Home, Eye } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  type: string;
  area: number;
  unit: string;
  image: string;
  views: number;
  isVerified: boolean;
}

interface FeaturedListingsProps {
  properties?: Property[];
  className?: string;
}

// Mock data for demonstration
const mockProperties: Property[] = [
  {
    id: '1',
    title: 'Premium Residential Plot',
    price: 2500000,
    location: 'Whitefield, Bangalore',
    type: 'Residential Plot',
    area: 1200,
    unit: 'sqft',
    image: '/api/placeholder/400/300',
    views: 245,
    isVerified: true,
  },
  {
    id: '2',
    title: 'Commercial Land',
    price: 5000000,
    location: 'Electronic City, Bangalore',
    type: 'Commercial Plot',
    area: 2400,
    unit: 'sqft',
    image: '/api/placeholder/400/300',
    views: 189,
    isVerified: true,
  },
  {
    id: '3',
    title: 'Agricultural Farmland',
    price: 1800000,
    location: 'Devanahalli, Bangalore',
    type: 'Agricultural Land',
    area: 2,
    unit: 'acres',
    image: '/api/placeholder/400/300',
    views: 156,
    isVerified: false,
  },
  {
    id: '4',
    title: 'Villa Plot',
    price: 3200000,
    location: 'Sarjapur Road, Bangalore',
    type: 'Residential Plot',
    area: 1800,
    unit: 'sqft',
    image: '/api/placeholder/400/300',
    views: 298,
    isVerified: true,
  },
];

export function FeaturedListings({ properties = mockProperties, className }: FeaturedListingsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const itemsPerView = 3;
  const maxIndex = Math.max(0, properties.length - itemsPerView);

  const nextSlide = () => {
    setCurrentIndex(prev => Math.min(prev + 1, maxIndex));
  };

  const prevSlide = () => {
    setCurrentIndex(prev => Math.max(prev - 1, 0));
  };

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-text-primary mb-2">
            Featured Properties
          </h2>
          <p className="text-text-secondary">
            Discover premium properties near you
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            onClick={prevSlide}
            disabled={currentIndex === 0}
            className="w-10 h-10"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={nextSlide}
            disabled={currentIndex >= maxIndex}
            className="w-10 h-10"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="overflow-hidden">
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)` }}
        >
          {properties.map((property) => (
            <div key={property.id} className="w-1/3 flex-shrink-0 px-3">
              <Card className="overflow-hidden hover:shadow-card-hover transition-shadow duration-300">
                <div className="relative">
                  <div className="aspect-video bg-gray-light relative overflow-hidden">
                    {/* Placeholder for property image */}
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                      <Home className="w-12 h-12 text-primary/50" />
                    </div>
                    
                    {/* Verification Badge */}
                    {property.isVerified && (
                      <div className="absolute top-3 left-3 bg-success text-white px-2 py-1 rounded-lg text-xs font-medium">
                        ✓ Verified
                      </div>
                    )}
                    
                    {/* Views Count */}
                    <div className="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded-lg text-xs flex items-center">
                      <Eye className="w-3 h-3 mr-1" />
                      {property.views}
                    </div>
                  </div>
                </div>
                
                <CardContent className="p-4">
                  <div className="mb-3">
                    <h3 className="font-semibold text-text-primary mb-1 line-clamp-1">
                      {property.title}
                    </h3>
                    <div className="flex items-center text-text-secondary text-sm mb-2">
                      <MapPin className="w-4 h-4 mr-1" />
                      {property.location}
                    </div>
                    <p className="text-xs text-text-secondary bg-gray-light px-2 py-1 rounded inline-block">
                      {property.type}
                    </p>
                  </div>
                  
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <p className="text-lg font-bold text-primary">
                        {formatCurrency(property.price)}
                      </p>
                      <p className="text-sm text-text-secondary">
                        {property.area} {property.unit}
                      </p>
                    </div>
                  </div>
                  
                  <Link href={`/properties/${property.id}`}>
                    <Button variant="primary" className="w-full">
                      View Details
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
      
      {/* View All Button */}
      <div className="text-center mt-6">
        <Link href="/properties">
          <Button variant="outline">
            View All Properties
          </Button>
        </Link>
      </div>
    </div>
  );
}
