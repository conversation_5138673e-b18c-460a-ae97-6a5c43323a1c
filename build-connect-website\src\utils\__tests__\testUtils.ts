import { render, RenderOptions } from '@testing-library/react'
import { ReactElement, ReactNode } from 'react'
import { AuthProvider } from '@/context/AuthContext'
import { ThemeProvider } from '@/context/ThemeContext'

// Mock user data for testing
export const mockUsers = {
  buyer: {
    id: '1',
    name: 'Test Buyer',
    email: '<EMAIL>',
    role: 'buyer' as const,
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  broker: {
    id: '2',
    name: 'Test Broker',
    email: '<EMAIL>',
    role: 'broker' as const,
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    experience: 5,
    serviceAreas: ['Bangalore', 'Mumbai'],
    specializations: ['residential_plot', 'commercial_plot'],
    rating: 4.5,
    reviewCount: 25,
    totalSales: 50,
    commission: 2,
    portfolio: [],
    isApproved: true,
    approvedAt: '2024-01-01T00:00:00Z'
  },
  admin: {
    id: '3',
    name: 'Test Admin',
    email: '<EMAIL>',
    role: 'admin' as const,
    isVerified: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
}

// Mock property data for testing
export const mockProperties = {
  apartment: {
    id: '1',
    title: 'Beautiful 3BHK Apartment',
    description: 'Spacious apartment with modern amenities',
    price: 5000000,
    area: 1200,
    location: {
      address: '123 Main Street',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560001',
      coordinates: { lat: 12.9716, lng: 77.5946 }
    },
    propertyType: 'apartment' as const,
    status: 'available' as const,
    images: ['/test-image.jpg'],
    amenities: ['Parking', 'Gym', 'Swimming Pool'],
    features: ['Balcony', 'Modular Kitchen'],
    brokerId: '2',
    brokerName: 'Test Broker',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  villa: {
    id: '2',
    title: 'Luxury Villa with Garden',
    description: 'Beautiful villa with spacious garden and pool',
    price: 12000000,
    area: 2500,
    location: {
      address: '456 Garden Street',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001',
      coordinates: { lat: 19.0760, lng: 72.8777 }
    },
    propertyType: 'villa' as const,
    status: 'available' as const,
    images: ['/test-villa.jpg'],
    amenities: ['Parking', 'Garden', 'Swimming Pool', 'Security'],
    features: ['Private Pool', 'Garden', 'Terrace'],
    brokerId: '2',
    brokerName: 'Test Broker',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  }
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialUser?: typeof mockUsers.buyer | typeof mockUsers.broker | typeof mockUsers.admin | null
  theme?: 'light' | 'dark'
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { initialUser = null, theme = 'light', ...renderOptions } = options

  function Wrapper({ children }: { children: ReactNode }) {
    return (
      <ThemeProvider defaultTheme={theme}>
        <AuthProvider initialUser={initialUser}>
          {children}
        </AuthProvider>
      </ThemeProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Test utilities for form interactions
export const formUtils = {
  fillInput: async (input: HTMLElement, value: string) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.change(input, { target: { value } })
  },

  selectOption: async (select: HTMLElement, value: string) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.change(select, { target: { value } })
  },

  checkCheckbox: async (checkbox: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.click(checkbox)
  },

  submitForm: async (form: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.submit(form)
  }
}

// Test utilities for async operations
export const asyncUtils = {
  waitForElement: async (selector: string, timeout = 5000) => {
    const { waitFor } = await import('@testing-library/react')
    const { screen } = await import('@testing-library/react')
    
    return waitFor(() => screen.getByTestId(selector), { timeout })
  },

  waitForText: async (text: string, timeout = 5000) => {
    const { waitFor } = await import('@testing-library/react')
    const { screen } = await import('@testing-library/react')
    
    return waitFor(() => screen.getByText(text), { timeout })
  },

  waitForDisappear: async (element: HTMLElement, timeout = 5000) => {
    const { waitForElementToBeRemoved } = await import('@testing-library/react')
    
    return waitForElementToBeRemoved(element, { timeout })
  }
}

// Mock API responses
export const mockApiResponses = {
  login: {
    success: {
      user: mockUsers.buyer,
      token: 'mock-token'
    },
    error: {
      error: 'Invalid credentials'
    }
  },

  properties: {
    list: {
      properties: [mockProperties.apartment, mockProperties.villa],
      total: 2,
      page: 1,
      limit: 10
    },
    single: mockProperties.apartment,
    created: {
      ...mockProperties.apartment,
      id: 'new-property-id'
    }
  },

  brokers: {
    list: {
      brokers: [mockUsers.broker],
      total: 1,
      page: 1,
      limit: 10
    },
    single: mockUsers.broker
  }
}

// Test data generators
export const generateTestData = {
  user: (overrides = {}) => ({
    ...mockUsers.buyer,
    id: Math.random().toString(36).substr(2, 9),
    email: `test-${Date.now()}@example.com`,
    ...overrides
  }),

  property: (overrides = {}) => ({
    ...mockProperties.apartment,
    id: Math.random().toString(36).substr(2, 9),
    title: `Test Property ${Date.now()}`,
    ...overrides
  }),

  broker: (overrides = {}) => ({
    ...mockUsers.broker,
    id: Math.random().toString(36).substr(2, 9),
    email: `broker-${Date.now()}@example.com`,
    ...overrides
  })
}

// Mock fetch for API testing
export const mockFetch = (responses: Record<string, any>) => {
  global.fetch = jest.fn((url: string, options?: RequestInit) => {
    const method = options?.method || 'GET'
    const key = `${method} ${url}`
    
    const response = responses[key] || responses[url]
    
    if (!response) {
      return Promise.reject(new Error(`No mock response for ${key}`))
    }
    
    return Promise.resolve({
      ok: response.ok !== false,
      status: response.status || 200,
      json: () => Promise.resolve(response.data || response),
      text: () => Promise.resolve(JSON.stringify(response.data || response))
    } as Response)
  })
}

// Cleanup utilities
export const cleanup = {
  localStorage: () => {
    localStorage.clear()
  },

  sessionStorage: () => {
    sessionStorage.clear()
  },

  mocks: () => {
    jest.clearAllMocks()
  },

  all: () => {
    cleanup.localStorage()
    cleanup.sessionStorage()
    cleanup.mocks()
  }
}

// Performance testing utilities
export const performanceUtils = {
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now()
    renderFn()
    const end = performance.now()
    return end - start
  },

  measureAsyncOperation: async (operation: () => Promise<any>) => {
    const start = performance.now()
    await operation()
    const end = performance.now()
    return end - start
  }
}

// Accessibility testing utilities
export const a11yUtils = {
  checkFocusManagement: async (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    return Array.from(focusableElements).every(element => {
      const tabIndex = element.getAttribute('tabindex')
      return tabIndex !== '-1'
    })
  },

  checkAriaLabels: async (container: HTMLElement) => {
    const interactiveElements = container.querySelectorAll(
      'button, [role="button"], input, select, textarea'
    )
    
    return Array.from(interactiveElements).every(element => {
      return element.getAttribute('aria-label') || 
             element.getAttribute('aria-labelledby') ||
             element.textContent?.trim()
    })
  }
}

// Export all utilities
export * from '@testing-library/react'
export { renderWithProviders as render }
