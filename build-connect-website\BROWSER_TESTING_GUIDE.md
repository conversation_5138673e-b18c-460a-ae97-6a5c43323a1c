# Browser Testing Guide - Authentication & Role-Based Redirects

## 🎯 Testing Objectives

1. **Login Flow Testing**
   - Test user registration
   - Test user login with different roles
   - Test authentication persistence
   - Test logout functionality

2. **Role-Based Redirect Testing**
   - Test buyer role redirects
   - Test broker role redirects
   - Test contractor role redirects
   - Test admin role redirects

## 🔧 Prerequisites

- ✅ Backend running on `http://localhost:8080`
- ✅ Frontend running on `http://localhost:3008`
- ✅ Browser with developer tools open

## 📋 Test Cases

### Test Case 1: User Registration Flow

**Steps:**
1. Navigate to: `http://localhost:3008/auth/register`
2. Fill in registration form:
   - Name: `Test User`
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
   - Role: `buyer`
   - Phone: `9876543210`
3. Submit form
4. Check for success message
5. Check browser network tab for API calls

**Expected Results:**
- ✅ Registration success message
- ✅ Redirect to login page or dashboard
- ✅ Network call to `/api/auth/register` returns 200
- ✅ Backend call to `/user-service/api/v1/signup` succeeds

### Test Case 2: Login Flow - Buyer Role

**Steps:**
1. Navigate to: `http://localhost:3008/auth/login`
2. Enter credentials:
   - Email: `<EMAIL>`
   - Password: `TestPassword123!`
3. Submit form
4. Check redirect behavior
5. Check browser cookies
6. Check session storage

**Expected Results:**
- ✅ Login success message
- ✅ Redirect to buyer dashboard (`/dashboard` or `/`)
- ✅ Cookies set: `access-token`, `session-id`
- ✅ NextAuth session created

### Test Case 3: Login Flow - Broker Role

**Steps:**
1. Register a new user with role `broker`
2. Login with broker credentials
3. Check redirect behavior

**Expected Results:**
- ✅ Redirect to broker dashboard (`/broker/dashboard` or `/dashboard`)
- ✅ Access to broker-specific features

### Test Case 4: Login Flow - Contractor Role

**Steps:**
1. Register a new user with role `contractor`
2. Login with contractor credentials
3. Check redirect behavior

**Expected Results:**
- ✅ Redirect to contractor dashboard (`/contractor/dashboard` or `/dashboard`)
- ✅ Access to contractor-specific features

### Test Case 5: Protected Route Access

**Steps:**
1. Without logging in, try to access: `http://localhost:3008/brokers`
2. Check if redirected to login
3. Login and try again
4. Check access

**Expected Results:**
- ✅ Unauthenticated users redirected to login
- ✅ Authenticated users can access protected routes

### Test Case 6: Session Persistence

**Steps:**
1. Login successfully
2. Refresh the page
3. Navigate to different pages
4. Close and reopen browser tab

**Expected Results:**
- ✅ Session persists across page refreshes
- ✅ User remains logged in

### Test Case 7: Logout Flow

**Steps:**
1. Login successfully
2. Click logout button
3. Check redirect behavior
4. Try to access protected routes

**Expected Results:**
- ✅ Successful logout
- ✅ Redirect to login page
- ✅ Cookies cleared
- ✅ Cannot access protected routes

## 🔍 Debugging Checklist

### Browser Developer Tools

**Console Tab:**
- Check for JavaScript errors
- Look for authentication-related logs
- Check for API call errors

**Network Tab:**
- Monitor API calls to `/api/auth/*`
- Check response status codes
- Verify request/response payloads

**Application Tab:**
- Check cookies: `access-token`, `session-id`
- Check localStorage/sessionStorage
- Check service worker status

**Sources Tab:**
- Set breakpoints in authentication code
- Step through login flow
- Check variable values

### Common Issues & Solutions

**Issue: Login form doesn't submit**
- Check form validation
- Check network connectivity
- Check console for errors

**Issue: Login succeeds but no redirect**
- Check NextAuth configuration
- Check role-based redirect logic
- Check for JavaScript errors

**Issue: "Please log in to view brokers" message**
- This is expected behavior (backend requires auth)
- Verify user is actually logged in
- Check if JWT token is being sent

**Issue: Session not persisting**
- Check cookie settings
- Check NextAuth configuration
- Check browser privacy settings

## 🧪 Manual Testing Script

```javascript
// Run in browser console to check authentication state

// Check NextAuth session
fetch('/api/auth/session')
  .then(r => r.json())
  .then(session => console.log('NextAuth Session:', session));

// Check cookies
console.log('Cookies:', document.cookie);

// Check if user is authenticated
console.log('Access Token Cookie:', 
  document.cookie.split('; ')
    .find(row => row.startsWith('access-token='))
);

// Test protected API call
fetch('/api/auth/me')
  .then(r => r.json())
  .then(data => console.log('User Profile:', data))
  .catch(err => console.log('Profile Error:', err));
```

## 📊 Test Results Template

### Registration Test Results
- [ ] Form loads correctly
- [ ] Validation works
- [ ] API call succeeds
- [ ] Success message shown
- [ ] Redirect works

### Login Test Results
- [ ] Form loads correctly
- [ ] Buyer login works
- [ ] Broker login works
- [ ] Contractor login works
- [ ] Cookies set correctly
- [ ] Session created

### Role-Based Redirect Results
- [ ] Buyer → Dashboard
- [ ] Broker → Broker Dashboard
- [ ] Contractor → Contractor Dashboard
- [ ] Admin → Admin Dashboard

### Protected Routes Results
- [ ] Unauthenticated users blocked
- [ ] Authenticated users allowed
- [ ] Proper error messages

### Session Persistence Results
- [ ] Survives page refresh
- [ ] Survives navigation
- [ ] Survives browser tab close/open

### Logout Results
- [ ] Logout button works
- [ ] Cookies cleared
- [ ] Session destroyed
- [ ] Redirect to login

## 🚀 Next Steps After Testing

1. **If tests pass:** Continue with component updates
2. **If tests fail:** Debug specific issues
3. **Document any bugs found**
4. **Create user acceptance criteria**
