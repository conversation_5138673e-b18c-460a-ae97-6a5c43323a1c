# Testing Guide for BUILD-CONNECT

This document provides comprehensive information about testing in the BUILD-CONNECT application.

## Table of Contents

- [Testing Strategy](#testing-strategy)
- [Test Types](#test-types)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Performance Testing](#performance-testing)
- [Accessibility Testing](#accessibility-testing)
- [CI/CD Pipeline](#cicd-pipeline)
- [Best Practices](#best-practices)

## Testing Strategy

Our testing strategy follows the testing pyramid approach:

```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/__________\ Unit Tests (Many)
```

### Test Coverage Goals

- **Unit Tests**: 80%+ coverage
- **Integration Tests**: Critical user flows
- **E2E Tests**: Happy paths and critical business flows
- **Performance Tests**: Core Web Vitals compliance
- **Accessibility Tests**: WCAG 2.1 AA compliance

## Test Types

### 1. Unit Tests

Test individual components, functions, and hooks in isolation.

**Location**: `src/**/__tests__/` or `src/**/*.test.{ts,tsx}`

**Tools**: Jest, React Testing Library

**Example**:
```typescript
import { render, screen } from '@testing-library/react'
import { Button } from '../Button'

test('renders button with text', () => {
  render(<Button>Click me</Button>)
  expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument()
})
```

### 2. Integration Tests

Test how multiple components work together.

**Location**: `src/**/__tests__/integration/`

**Tools**: Jest, React Testing Library, MSW (Mock Service Worker)

### 3. End-to-End Tests

Test complete user workflows in a real browser environment.

**Location**: `e2e/`

**Tools**: Playwright

**Example**:
```typescript
import { test, expect } from '@playwright/test'

test('user can login successfully', async ({ page }) => {
  await page.goto('/auth/login')
  await page.fill('[data-testid="email"]', '<EMAIL>')
  await page.fill('[data-testid="password"]', 'password123')
  await page.click('[data-testid="login-button"]')
  await expect(page).toHaveURL('/dashboard')
})
```

### 4. Performance Tests

Test application performance and Core Web Vitals.

**Location**: `e2e/performance.spec.ts`

**Metrics**:
- Largest Contentful Paint (LCP) < 2.5s
- First Input Delay (FID) < 100ms
- Cumulative Layout Shift (CLS) < 0.1

### 5. Accessibility Tests

Test application accessibility compliance.

**Location**: `e2e/accessibility.spec.ts`

**Tools**: axe-core, Playwright

## Running Tests

### Unit Tests

```bash
# Run all unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm run test Button.test.tsx

# Run tests for specific component
npm run test:components
```

### Integration Tests

```bash
# Run integration tests
npm run test:integration
```

### End-to-End Tests

```bash
# Install Playwright browsers (first time only)
npm run playwright:install

# Run all E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode
npm run test:e2e:headed

# Run specific test file
npx playwright test auth.spec.ts
```

### Performance Tests

```bash
# Run performance tests
npm run test:performance
```

### Accessibility Tests

```bash
# Run accessibility tests
npm run test:accessibility
```

### All Tests

```bash
# Run all tests (CI mode)
npm run test:all
```

## Writing Tests

### Unit Test Guidelines

1. **Test Behavior, Not Implementation**
   ```typescript
   // ❌ Bad - testing implementation
   expect(component.state.isLoading).toBe(true)
   
   // ✅ Good - testing behavior
   expect(screen.getByText('Loading...')).toBeInTheDocument()
   ```

2. **Use Descriptive Test Names**
   ```typescript
   // ❌ Bad
   test('button test', () => {})
   
   // ✅ Good
   test('should disable button when loading prop is true', () => {})
   ```

3. **Follow AAA Pattern**
   ```typescript
   test('should show error message on invalid input', () => {
     // Arrange
     render(<LoginForm />)
     
     // Act
     fireEvent.click(screen.getByRole('button', { name: /login/i }))
     
     // Assert
     expect(screen.getByText(/email is required/i)).toBeInTheDocument()
   })
   ```

### E2E Test Guidelines

1. **Use Page Object Model**
   ```typescript
   class LoginPage {
     constructor(private page: Page) {}
     
     async login(email: string, password: string) {
       await this.page.fill('[data-testid="email"]', email)
       await this.page.fill('[data-testid="password"]', password)
       await this.page.click('[data-testid="login-button"]')
     }
   }
   ```

2. **Use Data Test IDs**
   ```typescript
   // ❌ Bad - fragile selector
   await page.click('.btn-primary')
   
   // ✅ Good - stable selector
   await page.click('[data-testid="login-button"]')
   ```

3. **Wait for Elements Properly**
   ```typescript
   // ❌ Bad - arbitrary timeout
   await page.waitForTimeout(1000)
   
   // ✅ Good - wait for specific condition
   await page.waitForSelector('[data-testid="dashboard"]')
   ```

## Performance Testing

### Core Web Vitals

We monitor and test the following metrics:

1. **Largest Contentful Paint (LCP)**
   - Target: < 2.5 seconds
   - Measures loading performance

2. **First Input Delay (FID)**
   - Target: < 100 milliseconds
   - Measures interactivity

3. **Cumulative Layout Shift (CLS)**
   - Target: < 0.1
   - Measures visual stability

### Performance Test Example

```typescript
test('should meet Core Web Vitals thresholds', async ({ page }) => {
  await page.goto('/')
  
  const metrics = await page.evaluate(() => {
    return new Promise((resolve) => {
      new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lcp = entries.find(entry => entry.entryType === 'largest-contentful-paint')
        resolve({ lcp: lcp?.startTime })
      }).observe({ entryTypes: ['largest-contentful-paint'] })
    })
  })
  
  expect(metrics.lcp).toBeLessThan(2500)
})
```

## Accessibility Testing

### WCAG 2.1 AA Compliance

We test for:

- Keyboard navigation
- Screen reader compatibility
- Color contrast ratios
- Focus management
- Semantic HTML
- ARIA attributes

### Accessibility Test Example

```typescript
test('should not have accessibility violations', async ({ page }) => {
  await page.goto('/')
  
  const accessibilityScanResults = await new AxeBuilder({ page }).analyze()
  
  expect(accessibilityScanResults.violations).toEqual([])
})
```

## CI/CD Pipeline

Our CI/CD pipeline runs the following checks:

1. **Code Quality**
   - ESLint
   - TypeScript type checking
   - Prettier formatting

2. **Testing**
   - Unit tests with coverage
   - Integration tests
   - E2E tests across multiple browsers
   - Performance tests
   - Accessibility tests

3. **Security**
   - npm audit
   - Snyk vulnerability scanning

4. **Build**
   - Next.js build
   - Bundle analysis

5. **Deployment**
   - Automatic deployment to staging
   - Manual deployment to production

## Best Practices

### General Testing

1. **Write Tests First** (TDD approach when possible)
2. **Keep Tests Simple** - One assertion per test when possible
3. **Use Meaningful Test Data** - Avoid magic numbers and strings
4. **Clean Up After Tests** - Reset state between tests
5. **Test Edge Cases** - Empty states, error conditions, boundary values

### Component Testing

1. **Test User Interactions** - Click, type, hover, etc.
2. **Test Props** - Different prop combinations
3. **Test State Changes** - Loading, error, success states
4. **Test Accessibility** - Keyboard navigation, screen readers

### E2E Testing

1. **Test Critical User Journeys** - Login, property search, broker contact
2. **Use Realistic Data** - Real-world scenarios
3. **Test Across Browsers** - Chrome, Firefox, Safari
4. **Test Mobile Devices** - Responsive design
5. **Parallel Execution** - Run tests in parallel for speed

### Performance Testing

1. **Test on Slow Networks** - 3G, slow 4G
2. **Test with Large Datasets** - Many properties, images
3. **Monitor Bundle Size** - Keep JavaScript bundles small
4. **Test Image Optimization** - Lazy loading, responsive images

### Accessibility Testing

1. **Test with Keyboard Only** - No mouse interactions
2. **Test with Screen Readers** - VoiceOver, NVDA, JAWS
3. **Test Color Contrast** - Automated and manual checks
4. **Test Focus Management** - Logical tab order

## Debugging Tests

### Unit Tests

```bash
# Debug specific test
npm run test -- --testNamePattern="should render correctly" --verbose

# Debug with Node debugger
node --inspect-brk node_modules/.bin/jest --runInBand
```

### E2E Tests

```bash
# Run with headed browser
npm run test:e2e:headed

# Debug specific test
npx playwright test auth.spec.ts --debug

# Generate trace
npx playwright test --trace on
```

## Test Data Management

### Mock Data

Use consistent mock data across tests:

```typescript
// testUtils.ts
export const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'buyer'
}

export const mockProperty = {
  id: '1',
  title: 'Test Property',
  price: 1000000,
  location: { city: 'Bangalore', state: 'Karnataka' }
}
```

### Test Database

For integration tests, use a separate test database:

```typescript
// jest.setup.js
beforeEach(async () => {
  await resetTestDatabase()
})
```

## Continuous Improvement

1. **Monitor Test Performance** - Keep test execution time reasonable
2. **Review Test Coverage** - Aim for meaningful coverage, not just numbers
3. **Update Tests Regularly** - Keep tests in sync with code changes
4. **Refactor Tests** - Remove duplicate code, improve readability
5. **Learn from Failures** - Analyze test failures to improve test quality

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/)
- [Web Vitals](https://web.dev/vitals/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)

---

For questions or issues with testing, please reach out to the development team or create an issue in the repository.
