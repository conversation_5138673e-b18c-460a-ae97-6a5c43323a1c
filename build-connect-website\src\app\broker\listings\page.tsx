'use client';

import { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Loading } from '@/components/ui/Loading';
import {
  Home,
  Plus,
  Search,
  Filter,
  Edit,
  Eye,
  Trash2,
  MapPin,
  Calendar,
  DollarSign,
  MoreVertical,
  Star,
  MessageSquare,
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { sitesService } from '@/services/sites.service';
import { formatCurrency, formatRelativeTime } from '@/lib/utils';
import Link from 'next/link';

interface Property {
  _id: string;
  name: string;
  location: string;
  price: number;
  propertyType: string;
  status: 'active' | 'sold' | 'rented' | 'inactive';
  images: string[];
  createdAt: string;
  views: number;
  inquiries: number;
  description: string;
}

const PROPERTY_STATUS_CONFIG = {
  active: { label: 'Active', color: 'text-green-600', bgColor: 'bg-green-50' },
  sold: { label: 'Sold', color: 'text-blue-600', bgColor: 'bg-blue-50' },
  rented: { label: 'Rented', color: 'text-purple-600', bgColor: 'bg-purple-50' },
  inactive: { label: 'Inactive', color: 'text-gray-600', bgColor: 'bg-gray-50' },
};

export default function BrokerListingsPage() {
  const { user } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    loadProperties();
  }, []);

  const loadProperties = async () => {
    try {
      setIsLoading(true);
      const response = await sitesService.getSites();
      if (response.success && response.data) {
        // Mock data for demonstration - in real app, this would come from broker's properties
        const mockProperties: Property[] = [
          {
            _id: '1',
            name: '3BHK Luxury Apartment',
            location: 'Whitefield, Bangalore',
            price: 8500000,
            propertyType: 'Apartment',
            status: 'active',
            images: ['/api/placeholder/400/300'],
            createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            views: 245,
            inquiries: 12,
            description: 'Spacious 3BHK apartment with modern amenities'
          },
          {
            _id: '2',
            name: '2BHK Modern Flat',
            location: 'Koramangala, Bangalore',
            price: 6200000,
            propertyType: 'Apartment',
            status: 'sold',
            images: ['/api/placeholder/400/300'],
            createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
            views: 189,
            inquiries: 8,
            description: 'Well-designed 2BHK flat in prime location'
          },
          {
            _id: '3',
            name: 'Independent Villa',
            location: 'HSR Layout, Bangalore',
            price: 15000000,
            propertyType: 'Villa',
            status: 'active',
            images: ['/api/placeholder/400/300'],
            createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            views: 156,
            inquiries: 15,
            description: 'Beautiful independent villa with garden'
          },
        ];
        setProperties(mockProperties);
      }
    } catch (error) {
      console.error('Error loading properties:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const breadcrumbs = [
    { label: 'Broker Dashboard', href: '/broker/dashboard' },
    { label: 'My Listings', current: true }
  ];

  if (isLoading) {
    return (
      <ProtectedRoute allowedRoles={['broker']}>
        <DashboardLayout
          breadcrumbs={breadcrumbs}
          title="My Property Listings"
          description="Manage your property listings and track performance"
        >
          <Loading />
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['broker']}>
      <DashboardLayout
        breadcrumbs={breadcrumbs}
        title="My Property Listings"
        description="Manage your property listings and track performance"
        actions={
          <Link href="/properties/add">
            <Button variant="primary">
              <Plus className="w-4 h-4 mr-2" />
              Add New Property
            </Button>
          </Link>
        }
      >
        <div className="space-y-6">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Listings</p>
                    <p className="text-2xl font-bold text-gray-900">{properties.length}</p>
                  </div>
                  <Home className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Listings</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {properties.filter(p => p.status === 'active').length}
                    </p>
                  </div>
                  <Star className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Views</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {properties.reduce((sum, p) => sum + p.views, 0)}
                    </p>
                  </div>
                  <Eye className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Inquiries</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {properties.reduce((sum, p) => sum + p.inquiries, 0)}
                    </p>
                  </div>
                  <MessageSquare className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search properties..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="sm:w-48">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="sold">Sold</option>
                    <option value="rented">Rented</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Properties List */}
          {filteredProperties.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Home className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {properties.length === 0 ? 'No properties listed yet' : 'No properties found'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {properties.length === 0 
                    ? 'Start by adding your first property listing to attract potential buyers.'
                    : 'Try adjusting your search or filters.'
                  }
                </p>
                {properties.length === 0 && (
                  <Link href="/properties/add">
                    <Button variant="primary">
                      Add Your First Property
                    </Button>
                  </Link>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-6">
              {filteredProperties.map((property) => {
                const statusConfig = PROPERTY_STATUS_CONFIG[property.status];
                return (
                  <Card key={property._id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-6">
                        {/* Property Image */}
                        <div className="w-32 h-24 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                          {property.images[0] ? (
                            <img
                              src={property.images[0]}
                              alt={property.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Home className="w-8 h-8 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Property Details */}
                        <div className="flex-1">
                          <div className="flex items-start justify-between">
                            <div>
                              <div className="flex items-center gap-3 mb-2">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  {property.name}
                                </h3>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusConfig.color} ${statusConfig.bgColor}`}>
                                  {statusConfig.label}
                                </span>
                              </div>
                              
                              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                                <div className="flex items-center gap-1">
                                  <MapPin className="w-4 h-4" />
                                  {property.location}
                                </div>
                                <div className="flex items-center gap-1">
                                  <Calendar className="w-4 h-4" />
                                  Listed {formatRelativeTime(property.createdAt)}
                                </div>
                              </div>

                              <p className="text-2xl font-bold text-gray-900 mb-2">
                                {formatCurrency(property.price)}
                              </p>

                              <p className="text-gray-600 mb-3 line-clamp-2">
                                {property.description}
                              </p>

                              <div className="flex items-center gap-4 text-sm text-gray-600">
                                <div className="flex items-center gap-1">
                                  <Eye className="w-4 h-4" />
                                  {property.views} views
                                </div>
                                <div className="flex items-center gap-1">
                                  <MessageSquare className="w-4 h-4" />
                                  {property.inquiries} inquiries
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-2 ml-4">
                              <Link href={`/properties/${property._id}`}>
                                <Button variant="outline" size="sm">
                                  <Eye className="w-4 h-4 mr-1" />
                                  View
                                </Button>
                              </Link>
                              <Link href={`/properties/${property._id}/edit`}>
                                <Button variant="outline" size="sm">
                                  <Edit className="w-4 h-4 mr-1" />
                                  Edit
                                </Button>
                              </Link>
                              <Button variant="outline" size="sm">
                                <MoreVertical className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
