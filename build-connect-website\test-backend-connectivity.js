/**
 * Backend Connectivity Test
 * Tests connection to the Build Connect backend services
 */

// Use built-in fetch (Node.js 18+) or polyfill
const fetch = globalThis.fetch || require('node-fetch');

const API_BASE_URL = 'http://localhost:8080';

// Test endpoints
const endpoints = [
  {
    name: 'API Gateway Health',
    url: `${API_BASE_URL}/health`,
    method: 'GET',
  },
  {
    name: 'User Service Health',
    url: `${API_BASE_URL}/user-service/health`,
    method: 'GET',
  },
  {
    name: 'Site Service Health',
    url: `${API_BASE_URL}/site-service/health`,
    method: 'GET',
  },
  {
    name: 'Admin Service Health',
    url: `${API_BASE_URL}/admin-service/health`,
    method: 'GET',
  },
];

async function testEndpoint(endpoint) {
  try {
    console.log(`\n🔍 Testing: ${endpoint.name}`);
    console.log(`   URL: ${endpoint.url}`);

    const response = await fetch(endpoint.url, {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 5000,
    });

    if (response.ok) {
      const data = await response.text();
      console.log(`   ✅ Status: ${response.status} ${response.statusText}`);
      console.log(
        `   📄 Response: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`
      );
      return true;
    } else {
      console.log(`   ❌ Status: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.log(
        `   📄 Error: ${errorText.substring(0, 100)}${errorText.length > 100 ? '...' : ''}`
      );
      return false;
    }
  } catch (error) {
    console.log(`   ❌ Connection Error: ${error.message}`);
    return false;
  }
}

async function testAuthEndpoints() {
  console.log('\n🔐 Testing Authentication Endpoints...');

  // Test registration endpoint
  try {
    console.log('\n📝 Testing Registration Endpoint');
    const registerResponse = await fetch(
      `${API_BASE_URL}/user-service/api/v1/signup`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test User',
          email: `test-${Date.now()}@example.com`,
          password: 'testpassword123',
          role: 'buyer',
        }),
        timeout: 10000,
      }
    );

    console.log(
      `   Status: ${registerResponse.status} ${registerResponse.statusText}`
    );

    if (registerResponse.status === 201 || registerResponse.status === 200) {
      console.log('   ✅ Registration endpoint is working');
      const data = await registerResponse.json();
      console.log(`   📄 Response structure: ${Object.keys(data).join(', ')}`);
    } else {
      console.log('   ⚠️  Registration endpoint returned non-success status');
      const errorText = await registerResponse.text();
      console.log(`   📄 Error: ${errorText.substring(0, 200)}`);
    }
  } catch (error) {
    console.log(`   ❌ Registration test failed: ${error.message}`);
  }

  // Test login endpoint
  try {
    console.log('\n🔑 Testing Login Endpoint');
    const loginResponse = await fetch(
      `${API_BASE_URL}/user-service/api/v1/login`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword',
        }),
        timeout: 10000,
      }
    );

    console.log(
      `   Status: ${loginResponse.status} ${loginResponse.statusText}`
    );

    if (loginResponse.status === 401 || loginResponse.status === 400) {
      console.log(
        '   ✅ Login endpoint is responding (expected failure for wrong credentials)'
      );
    } else if (loginResponse.status === 200) {
      console.log('   ✅ Login endpoint is working');
    } else {
      console.log('   ⚠️  Login endpoint returned unexpected status');
    }

    const responseText = await loginResponse.text();
    console.log(`   📄 Response: ${responseText.substring(0, 200)}`);
  } catch (error) {
    console.log(`   ❌ Login test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🚀 Build Connect Backend Connectivity Test');
  console.log('==========================================');

  let successCount = 0;
  let totalTests = endpoints.length;

  // Test basic endpoints
  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint);
    if (success) successCount++;
  }

  // Test authentication endpoints
  await testAuthEndpoints();

  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`✅ Successful: ${successCount}/${totalTests} basic endpoints`);
  console.log(`🔗 API Gateway: ${API_BASE_URL}`);

  if (successCount === 0) {
    console.log('\n❌ No backend services are responding!');
    console.log('💡 Make sure to:');
    console.log('   1. Start the backend services');
    console.log('   2. Check if API Gateway is running on port 8080');
    console.log('   3. Verify Docker containers are up (if using Docker)');
    console.log('   4. Check firewall settings');
  } else if (successCount < totalTests) {
    console.log('\n⚠️  Some services are not responding');
    console.log('💡 Check individual service logs for issues');
  } else {
    console.log('\n🎉 All basic endpoints are responding!');
    console.log('💡 You can now test the frontend authentication');
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', reason);
});

main().catch(console.error);
