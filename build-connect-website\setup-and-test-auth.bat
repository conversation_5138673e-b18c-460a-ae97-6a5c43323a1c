@echo off
REM BUILD-CONNECT Authentication Testing Setup Script for Windows

echo 🚀 BUILD-CONNECT Authentication Testing Setup
echo ==============================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo ✅ Node.js and npm are installed

REM Check if we're in the right directory
if not exist package.json (
    echo ❌ package.json not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

echo ✅ Found package.json

REM Install dependencies
echo ℹ️  Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Check if frontend is running
echo ℹ️  Checking if frontend is running on port 3000...
curl -s http://localhost:3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend is running on port 3000
    set FRONTEND_RUNNING=true
) else (
    echo ⚠️  Frontend is not running on port 3000
    set FRONTEND_RUNNING=false
)

REM Start frontend if not running
if "%FRONTEND_RUNNING%"=="false" (
    echo ℹ️  Starting frontend server...
    echo ⚠️  This will start the frontend in a new window.
    echo ⚠️  You can close it later to stop the frontend.
    
    REM Start frontend in new window
    start "BUILD-CONNECT Frontend" cmd /c "npm run dev"
    
    REM Wait for frontend to start
    echo ℹ️  Waiting for frontend to start...
    timeout /t 10 /nobreak >nul
    
    REM Check again if frontend is running
    for /l %%i in (1,1,15) do (
        curl -s http://localhost:3000 >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ Frontend started successfully
            set FRONTEND_RUNNING=true
            goto :frontend_ready
        )
        timeout /t 2 /nobreak >nul
        echo .
    )
    
    :frontend_ready
    if "%FRONTEND_RUNNING%"=="false" (
        echo ❌ Failed to start frontend server
        pause
        exit /b 1
    )
)

REM Run authentication tests
echo ℹ️  Running authentication tests...
echo.

node run-auth-tests.js
if %errorlevel% neq 0 (
    echo ❌ Authentication tests failed
    pause
    exit /b 1
)

echo.
echo ✅ 🎉 All done! Authentication testing completed.

echo.
echo ℹ️  Test files created:
echo   - test-backend-server.js (Test backend API server)
echo   - test-auth.js (Authentication test suite)
echo   - run-auth-tests.js (Test runner script)
echo   - AUTHENTICATION_TESTING.md (Testing documentation)
echo.
echo ℹ️  To run tests again: node run-auth-tests.js

pause
