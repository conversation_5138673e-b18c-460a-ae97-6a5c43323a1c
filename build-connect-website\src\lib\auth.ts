import { NextAuthOptions } from 'next-auth';
import Credentials<PERSON>rovider from 'next-auth/providers/credentials';
import { JWT } from 'next-auth/jwt';
import { User, UserRole } from '@/types';

// Extend the built-in session types
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: UserRole;
      isVerified: boolean;
      accessToken: string;
    };
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    isVerified: boolean;
    accessToken: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    role: UserRole;
    isVerified: boolean;
    accessToken: string;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Call the backend API to authenticate user
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/user-service/api/v1/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          });
          console.log('Login response:', response);

          if (!response.ok) {
            console.error('Login failed:', response.status, response.statusText);
            return null;
          }

          const data = await response.json();

          // Check if login was successful and we have an access token
          if (data && data.accessToken) {
            // Since the backend doesn't return user data in login response,
            // we'll create a minimal user object and fetch details later if needed
            // For now, we'll extract basic info from the email and set defaults
            const emailParts = credentials.email.split('@');
            const username = emailParts[0];

            return {
              id: data.sessionId || 'temp-id', // Use sessionId as temporary ID
              email: credentials.email,
              name: username, // Use email username as name for now
              role: 'admin' as UserRole, // Default role, will be updated when we get actual user data
              isVerified: true, // Default to false, will be updated when we get actual user data
              accessToken: data.accessToken,
            };
          }

          return null;
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.isVerified = user.isVerified;
        token.accessToken = user.accessToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.role = token.role;
        session.user.isVerified = token.isVerified;
        session.user.accessToken = token.accessToken;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Handle role-based redirects after login
      if (url.startsWith('/')) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

// Helper functions for role-based access control
export const hasRole = (userRole: UserRole, allowedRoles: UserRole[]): boolean => {
  return allowedRoles.includes(userRole);
};

export const isAdmin = (userRole: UserRole): boolean => {
  return userRole === 'admin';
};

export const isBroker = (userRole: UserRole): boolean => {
  return userRole === 'broker';
};

export const isContractor = (userRole: UserRole): boolean => {
  return userRole === 'contractor';
};

export const isBuyer = (userRole: UserRole): boolean => {
  return userRole === 'buyer';
};
