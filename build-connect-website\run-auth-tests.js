#!/usr/bin/env node

/**
 * Authentication Test Runner
 * This script starts the test backend server and runs authentication tests
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

console.log('🚀 BUILD-CONNECT Authentication Test Runner\n')

// Check if required files exist
const requiredFiles = [
  'test-backend-server.js',
  'test-auth.js'
]

for (const file of requiredFiles) {
  if (!fs.existsSync(path.join(__dirname, file))) {
    console.error(`❌ Required file not found: ${file}`)
    process.exit(1)
  }
}

// Function to run a command and return a promise
function runCommand(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    })

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code)
      } else {
        reject(new Error(`Command failed with exit code ${code}`))
      }
    })

    child.on('error', (error) => {
      reject(error)
    })

    return child
  })
}

// Function to wait for a specific time
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// Function to check if a port is in use
function isPortInUse(port) {
  return new Promise((resolve) => {
    const net = require('net')
    const server = net.createServer()
    
    server.listen(port, () => {
      server.once('close', () => {
        resolve(false) // Port is available
      })
      server.close()
    })
    
    server.on('error', () => {
      resolve(true) // Port is in use
    })
  })
}

async function main() {
  try {
    console.log('📋 Pre-flight checks...')
    
    // Check if ports are available
    const backendPort = 8000
    const frontendPort = 3000
    
    const backendInUse = await isPortInUse(backendPort)
    const frontendInUse = await isPortInUse(frontendPort)
    
    if (backendInUse) {
      console.log(`⚠️  Port ${backendPort} is already in use (this might be your existing backend)`)
    }
    
    if (!frontendInUse) {
      console.log(`⚠️  Port ${frontendPort} is not in use. Please start your Next.js frontend first:`)
      console.log(`   npm run dev`)
      console.log(`   Then run this test again.\n`)
      process.exit(1)
    }
    
    console.log('✅ Frontend is running on port 3000')
    
    let backendProcess = null
    
    if (!backendInUse) {
      console.log('🚀 Starting test backend server...')
      
      // Start the backend server
      backendProcess = spawn('node', ['test-backend-server.js'], {
        stdio: ['inherit', 'inherit', 'inherit'],
        detached: false
      })
      
      // Wait for backend to start
      console.log('⏳ Waiting for backend server to start...')
      await wait(3000)
      
      // Check if backend started successfully
      const axios = require('axios')
      try {
        await axios.get('http://localhost:8000/health', { timeout: 5000 })
        console.log('✅ Test backend server is running')
      } catch (error) {
        console.error('❌ Failed to start test backend server')
        if (backendProcess) {
          backendProcess.kill()
        }
        process.exit(1)
      }
    } else {
      console.log('✅ Using existing backend on port 8000')
    }
    
    console.log('\n🧪 Running authentication tests...\n')
    
    // Run the authentication tests
    try {
      await runCommand('node', ['test-auth.js'])
      console.log('\n✅ All tests completed successfully!')
    } catch (error) {
      console.error('\n❌ Some tests failed. Check the output above for details.')
    }
    
    // Clean up
    if (backendProcess && !backendInUse) {
      console.log('\n🧹 Cleaning up test backend server...')
      backendProcess.kill()
      await wait(1000)
    }
    
  } catch (error) {
    console.error('❌ Test runner error:', error.message)
    process.exit(1)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Test runner interrupted')
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Test runner terminated')
  process.exit(0)
})

// Run the main function
main().catch(error => {
  console.error('❌ Unexpected error:', error)
  process.exit(1)
})
