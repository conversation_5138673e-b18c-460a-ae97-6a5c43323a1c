#!/bin/bash

# BUILD-CONNECT Authentication Testing Setup Script

echo "🚀 BUILD-CONNECT Authentication Testing Setup"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_success "Node.js and npm are installed"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root directory."
    exit 1
fi

print_success "Found package.json"

# Install dependencies
print_status "Installing dependencies..."
if npm install; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Check if frontend is running
print_status "Checking if frontend is running on port 3000..."
if curl -s http://localhost:3000 > /dev/null 2>&1; then
    print_success "Frontend is running on port 3000"
    FRONTEND_RUNNING=true
else
    print_warning "Frontend is not running on port 3000"
    FRONTEND_RUNNING=false
fi

# Start frontend if not running
if [ "$FRONTEND_RUNNING" = false ]; then
    print_status "Starting frontend server..."
    print_warning "This will start the frontend in the background."
    print_warning "You can stop it later with: pkill -f 'next dev'"
    
    # Start frontend in background
    npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            print_success "Frontend started successfully"
            FRONTEND_RUNNING=true
            break
        fi
        sleep 2
        echo -n "."
    done
    echo ""
    
    if [ "$FRONTEND_RUNNING" = false ]; then
        print_error "Failed to start frontend server"
        if [ ! -z "$FRONTEND_PID" ]; then
            kill $FRONTEND_PID 2>/dev/null
        fi
        exit 1
    fi
fi

# Run authentication tests
print_status "Running authentication tests..."
echo ""

if node run-auth-tests.js; then
    print_success "Authentication tests completed successfully!"
else
    print_error "Authentication tests failed"
    exit 1
fi

echo ""
print_success "🎉 All done! Authentication testing completed."

# Cleanup instructions
if [ ! -z "$FRONTEND_PID" ]; then
    echo ""
    print_warning "Frontend server is still running in the background (PID: $FRONTEND_PID)"
    print_warning "To stop it, run: kill $FRONTEND_PID"
    print_warning "Or to stop all Next.js processes: pkill -f 'next dev'"
fi

echo ""
print_status "Test files created:"
echo "  - test-backend-server.js (Test backend API server)"
echo "  - test-auth.js (Authentication test suite)"
echo "  - run-auth-tests.js (Test runner script)"
echo "  - AUTHENTICATION_TESTING.md (Testing documentation)"
echo ""
print_status "To run tests again: node run-auth-tests.js"
