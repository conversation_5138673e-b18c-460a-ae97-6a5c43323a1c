# BUILD-CONNECT Website Deployment Guide

## Overview
This document provides comprehensive deployment instructions for the BUILD-CONNECT real estate platform website.

## Prerequisites

### System Requirements
- Node.js 18.x or higher
- npm 9.x or higher
- Git
- Modern web browser

### Environment Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Build the application: `npm run build`
5. Start the production server: `npm start`

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```env
# Application
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME=BUILD-CONNECT

# Database
DATABASE_URL=your_database_connection_string
REDIS_URL=your_redis_connection_string

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-domain.com

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email Service
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password

# File Storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=your_aws_region
AWS_S3_BUCKET=your_s3_bucket_name

# Payment Gateway
STRIPE_PUBLIC_KEY=your_stripe_public_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# AI Services
OPENAI_API_KEY=your_openai_api_key
DOCUMENT_AI_API_KEY=your_document_ai_key

# Analytics
GOOGLE_ANALYTICS_ID=your_ga_tracking_id
```

## Deployment Options

### 1. Vercel Deployment (Recommended)

#### Quick Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/build-connect)

#### Manual Deployment
1. Install Vercel CLI: `npm i -g vercel`
2. Login to Vercel: `vercel login`
3. Deploy: `vercel --prod`
4. Configure environment variables in Vercel dashboard
5. Set up custom domain (optional)

### 2. Netlify Deployment

1. Connect your GitHub repository to Netlify
2. Set build command: `npm run build`
3. Set publish directory: `out` (for static export) or `.next` (for SSR)
4. Configure environment variables
5. Deploy

### 3. AWS Deployment

#### Using AWS Amplify
1. Connect repository to AWS Amplify
2. Configure build settings
3. Set environment variables
4. Deploy

#### Using EC2 + PM2
1. Launch EC2 instance
2. Install Node.js and PM2
3. Clone repository
4. Install dependencies
5. Build application
6. Start with PM2: `pm2 start ecosystem.config.js`

### 4. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.local
    depends_on:
      - redis
      - postgres

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: buildconnect
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

## Performance Optimization

### Build Optimization
1. Enable compression: `gzip` and `brotli`
2. Optimize images with Next.js Image component
3. Use dynamic imports for code splitting
4. Enable tree shaking
5. Minimize bundle size

### Runtime Optimization
1. Enable caching headers
2. Use CDN for static assets
3. Implement service worker for offline support
4. Optimize database queries
5. Use Redis for session storage

### Monitoring
1. Set up error tracking (Sentry)
2. Configure performance monitoring
3. Set up uptime monitoring
4. Monitor Core Web Vitals

## Security Configuration

### HTTPS Setup
1. Obtain SSL certificate (Let's Encrypt recommended)
2. Configure HTTPS redirect
3. Set up HSTS headers
4. Enable secure cookies

### Security Headers
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]
```

## Database Setup

### PostgreSQL
1. Create database: `buildconnect`
2. Run migrations: `npm run db:migrate`
3. Seed initial data: `npm run db:seed`

### Redis
1. Configure for session storage
2. Set up caching
3. Configure rate limiting

## Testing

### Pre-deployment Testing
1. Run unit tests: `npm test`
2. Run integration tests: `npm run test:integration`
3. Run E2E tests: `npm run test:e2e`
4. Performance testing: `npm run test:performance`
5. Security testing: `npm run test:security`

### Post-deployment Testing
1. Smoke tests
2. Load testing
3. Security scanning
4. Accessibility testing
5. Cross-browser testing

## Monitoring and Maintenance

### Health Checks
- Application health endpoint: `/api/health`
- Database connectivity check
- External service availability
- Memory and CPU usage

### Logging
- Application logs
- Error logs
- Access logs
- Performance logs

### Backup Strategy
- Database backups (daily)
- File storage backups
- Configuration backups
- Disaster recovery plan

## Troubleshooting

### Common Issues
1. **Build failures**: Check Node.js version and dependencies
2. **Environment variables**: Verify all required variables are set
3. **Database connection**: Check connection string and network access
4. **Performance issues**: Monitor Core Web Vitals and optimize accordingly

### Support
- Documentation: `/docs`
- Issue tracker: GitHub Issues
- Support email: <EMAIL>

## Rollback Procedure

1. Identify the last known good deployment
2. Revert to previous version
3. Verify functionality
4. Investigate and fix issues
5. Redeploy when ready

## Scaling Considerations

### Horizontal Scaling
- Load balancer configuration
- Multiple application instances
- Database read replicas
- CDN setup

### Vertical Scaling
- Increase server resources
- Optimize database performance
- Cache optimization
- Code optimization

---

For additional support or questions, please contact the development team or refer to the project documentation.
