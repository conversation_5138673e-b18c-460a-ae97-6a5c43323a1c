/**
 * Test Login Response Structure
 */

const fetch = globalThis.fetch || require('node-fetch');

const BACKEND_URL = 'http://localhost:8080';

async function testLoginResponse() {
  console.log('🔍 Testing Login Response Structure...');
  
  // First register a test user
  const testUser = {
    name: 'Login Test User',
    email: `logintest-${Date.now()}@example.com`,
    password: 'TestPassword123!',
    role: 'buyer',
    phone: `98765${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
  };

  try {
    // Register
    console.log('📝 Registering test user...');
    const registerResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });

    if (!registerResponse.ok) {
      const error = await registerResponse.text();
      console.log(`❌ Registration failed: ${error}`);
      return;
    }

    const registerData = await registerResponse.json();
    console.log('✅ Registration successful');
    console.log('📄 Registration response structure:');
    console.log(JSON.stringify(registerData, null, 2));

    // Login
    console.log('\n🔑 Testing login...');
    const loginResponse = await fetch(`${BACKEND_URL}/user-service/api/v1/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });

    console.log(`Status: ${loginResponse.status} ${loginResponse.statusText}`);
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ Login successful');
      console.log('📄 Login response structure:');
      console.log(JSON.stringify(loginData, null, 2));
      
      // Check for different possible token field names
      const possibleTokenFields = ['token', 'accessToken', 'access_token', 'authToken', 'jwt'];
      console.log('\n🔍 Checking for token fields:');
      
      for (const field of possibleTokenFields) {
        if (loginData[field]) {
          console.log(`✅ Found token in field '${field}': ${loginData[field].substring(0, 20)}...`);
        } else {
          console.log(`❌ No token found in field '${field}'`);
        }
      }
      
      // Check nested objects
      if (loginData.data && typeof loginData.data === 'object') {
        console.log('\n🔍 Checking nested data object:');
        for (const field of possibleTokenFields) {
          if (loginData.data[field]) {
            console.log(`✅ Found token in data.${field}: ${loginData.data[field].substring(0, 20)}...`);
          }
        }
      }
      
      if (loginData.user && typeof loginData.user === 'object') {
        console.log('\n🔍 User object structure:');
        console.log(JSON.stringify(loginData.user, null, 2));
      }
      
    } else {
      const error = await loginResponse.text();
      console.log(`❌ Login failed: ${error}`);
    }

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }
}

testLoginResponse().catch(console.error);
