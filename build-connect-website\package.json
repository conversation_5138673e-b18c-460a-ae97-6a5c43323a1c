{"name": "build-connect-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:performance": "playwright test e2e/performance.spec.ts", "test:accessibility": "playwright test e2e/accessibility.spec.ts", "test:all": "npm run test:ci && npm run test:e2e", "playwright:install": "playwright install", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build", "test:unit": "jest --testPathPattern=src/", "test:integration": "jest --testPathPattern=__tests__/integration", "test:components": "jest --testPathPattern=components/", "test:hooks": "jest --testPathPattern=hooks/", "test:utils": "jest --testPathPattern=utils/", "test:backend": "node test-backend-server.js", "test:auth": "node test-auth.js", "test:auth-full": "concurrently \"npm run test:backend\" \"sleep 3 && npm run test:auth\" --kill-others --success first"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@stripe/stripe-js": "^7.8.0", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-devtools": "^5.84.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "15.4.5", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "socket.io-client": "^4.8.1", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "yup": "^1.7.0", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "colors": "^1.4.0", "concurrently": "^8.2.0", "cors": "^2.8.5", "eslint": "^9", "eslint-config-next": "15.4.5", "express": "^4.18.0", "jest": "^30.0.5", "jsonwebtoken": "^9.0.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.17", "typescript": "^5"}}