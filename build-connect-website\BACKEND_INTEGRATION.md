# Backend Integration Guide

This document provides comprehensive information about the backend integration for the BUILD-CONNECT platform.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [API Services](#api-services)
- [Authentication](#authentication)
- [Real-time Features](#real-time-features)
- [Data Synchronization](#data-synchronization)
- [Configuration](#configuration)
- [Development Setup](#development-setup)
- [API Documentation](#api-documentation)

## Architecture Overview

The BUILD-CONNECT frontend integrates with a comprehensive backend system that provides:

- **RESTful API**: Core business logic and data operations
- **WebSocket Server**: Real-time features and notifications
- **Authentication Service**: JWT-based authentication with refresh tokens
- **File Storage**: Image and document upload handling
- **Payment Processing**: Multiple payment gateway integration
- **Notification System**: Email, SMS, and push notifications

### Technology Stack

- **API Client**: Custom HTTP client with retry logic and error handling
- **State Management**: React Query for server state management
- **Real-time**: Socket.IO for WebSocket connections
- **Authentication**: JWT tokens with automatic refresh
- **File Upload**: Multipart form data with progress tracking
- **Caching**: Redis-based caching with TTL

## API Services

### 1. Authentication Service (`authService`)

Handles user authentication, registration, and profile management.

```typescript
import { authService } from '@/services/auth.service'

// Login
const response = await authService.login({
  email: '<EMAIL>',
  password: 'password123'
})

// Register
const response = await authService.register({
  name: 'John Doe',
  email: '<EMAIL>',
  password: 'password123',
  role: 'buyer'
})

// Get current user
const user = await authService.getCurrentUser()
```

### 2. Properties Service (`propertiesService`)

Manages property listings, search, and related operations.

```typescript
import { propertiesService } from '@/services/properties.service'

// Get properties with filters
const properties = await propertiesService.getProperties({
  city: 'Bangalore',
  propertyType: 'apartment',
  minPrice: 1000000,
  maxPrice: 5000000
})

// Create property
const property = await propertiesService.createProperty({
  title: 'Beautiful 3BHK Apartment',
  description: 'Spacious apartment with modern amenities',
  price: 5000000,
  area: 1200,
  location: {
    address: '123 Main Street',
    city: 'Bangalore',
    state: 'Karnataka',
    pincode: '560001'
  },
  propertyType: 'apartment',
  amenities: ['Parking', 'Gym', 'Swimming Pool']
})
```

### 3. Brokers Service (`brokersService`)

Handles broker profiles, verification, and communication.

```typescript
import { brokersService } from '@/services/brokers.service'

// Get brokers
const brokers = await brokersService.getBrokers({
  city: 'Mumbai',
  specializations: ['residential_plot'],
  minRating: 4.0
})

// Apply to become broker
const application = await brokersService.applyToBroker({
  personalInfo: {
    fullName: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+91-9876543210'
  },
  businessInfo: {
    companyName: 'Smith Properties',
    yearsOfExperience: 5,
    specializations: ['residential_plot', 'commercial_plot']
  }
})
```

### 4. Site Scouting Service (`siteScoutingService`)

Manages site scouting requests and responses.

```typescript
import { siteScoutingService } from '@/services/site-scouting.service'

// Create site scout request
const request = await siteScoutingService.createSiteScoutRequest({
  title: 'Find 3BHK Apartment in Bangalore',
  description: 'Looking for a spacious apartment with good amenities',
  location: {
    city: 'Bangalore',
    state: 'Karnataka'
  },
  budget: { min: 3000000, max: 5000000 },
  requirements: {
    propertyType: 'apartment',
    minArea: 1000,
    maxArea: 1500,
    amenities: ['Parking', 'Gym']
  }
})
```

### 5. Payments Service (`paymentsService`)

Handles payment processing and subscription management.

```typescript
import { paymentsService } from '@/services/payments.service'

// Create payment intent
const intent = await paymentsService.createPaymentIntent({
  amount: 5000000,
  currency: 'INR',
  description: 'Property booking payment'
})

// Process payment
const payment = await paymentsService.processPayment({
  paymentIntentId: intent.id,
  paymentMethodId: 'pm_1234567890'
})
```

### 6. Notifications Service (`notificationsService`)

Manages user notifications and preferences.

```typescript
import { notificationsService } from '@/services/notifications.service'

// Get notifications
const notifications = await notificationsService.getNotifications({
  category: 'property',
  read: false
})

// Update notification preferences
await notificationsService.updateNotificationPreferences({
  email: {
    enabled: true,
    frequency: 'daily',
    categories: {
      property: true,
      broker: true,
      payment: true
    }
  }
})
```

## Authentication

### JWT Token Management

The authentication system uses JWT tokens with automatic refresh:

```typescript
import { tokenManager } from '@/lib/api-client'

// Set tokens after login
tokenManager.setTokens(accessToken, refreshToken)

// Get current token
const token = tokenManager.getToken()

// Check if token is expired
const isExpired = tokenManager.isTokenExpired(token)

// Clear tokens on logout
tokenManager.clearTokens()
```

### API Client Authentication

The API client automatically handles authentication:

```typescript
import { apiClient } from '@/lib/api-client'

// Set authentication tokens
apiClient.setAuth(accessToken, refreshToken)

// API calls automatically include authentication headers
const response = await apiClient.get('/protected-endpoint')

// Automatic token refresh on 401 errors
```

## Real-time Features

### WebSocket Connection

Real-time features are powered by Socket.IO:

```typescript
import { webSocketService } from '@/services/websocket.service'

// Connect to WebSocket server
await webSocketService.connect()

// Subscribe to events
webSocketService.on('notification', (notification) => {
  console.log('New notification:', notification)
})

webSocketService.on('propertyUpdated', (property) => {
  console.log('Property updated:', property)
})

// Emit events
webSocketService.emit('joinRoom', { room: 'property-123' })
```

### Real-time Events

The system supports various real-time events:

- **Notifications**: New notifications and alerts
- **Property Updates**: Property status changes and updates
- **Broker Status**: Online/offline status changes
- **Chat Messages**: Real-time messaging
- **Site Scouting**: Request assignments and updates
- **Payments**: Payment status updates

## Data Synchronization

### Offline Support

The sync service provides offline support and data synchronization:

```typescript
import { syncService } from '@/services/sync.service'

// Initialize with query client
syncService.initialize(queryClient)

// Add pending change for offline sync
syncService.addPendingChange({
  type: 'create',
  entity: 'property',
  entityId: 'new-property',
  data: propertyData
})

// Force sync all data
await syncService.forceSync()

// Get sync status
const status = syncService.getStatus()
```

### React Query Integration

Server state is managed using React Query:

```typescript
import { useProperties, useCreateProperty } from '@/hooks/api/useProperties'

function PropertiesPage() {
  // Fetch properties with caching
  const { data: properties, isLoading } = useProperties({
    city: 'Bangalore'
  })

  // Create property mutation
  const createProperty = useCreateProperty()

  const handleCreate = (propertyData) => {
    createProperty.mutate(propertyData)
  }

  return (
    // Component JSX
  )
}
```

## Configuration

### Environment Variables

Configure the backend integration using environment variables:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api
BACKEND_API_URL=http://localhost:8000
BACKEND_API_KEY=your-backend-api-key
NEXT_PUBLIC_WS_URL=ws://localhost:8001

# Authentication
JWT_SECRET=your-jwt-secret-key
TOKEN_EXPIRY=3600
REFRESH_TOKEN_EXPIRY=604800

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/build_connect

# Redis Cache
REDIS_URL=redis://localhost:6379

# File Storage
STORAGE_TYPE=local
UPLOAD_PATH=./uploads

# Payment Gateways
STRIPE_SECRET_KEY=sk_test_...
RAZORPAY_KEY_SECRET=your-razorpay-secret
```

### Backend Configuration

The backend configuration is centralized in `src/config/backend.config.ts`:

```typescript
import { CONFIG } from '@/config/backend.config'

// Access configuration
const apiUrl = CONFIG.API.BASE_URL
const jwtSecret = CONFIG.AUTH.JWT_SECRET
const redisUrl = CONFIG.CACHE.REDIS_URL
```

## Development Setup

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment

Copy `.env.example` to `.env.local` and update the values:

```bash
cp .env.example .env.local
```

### 3. Start Development Server

```bash
npm run dev
```

### 4. Backend Requirements

Ensure your backend server is running and provides the following endpoints:

- `POST /api/auth/login` - User authentication
- `GET /api/properties` - Property listings
- `POST /api/properties` - Create property
- `GET /api/brokers` - Broker listings
- `WebSocket` connection on configured port

## API Documentation

### Base URL

```
Production: https://api.buildconnect.com
Development: http://localhost:8000/api
```

### Authentication

All authenticated endpoints require a Bearer token:

```
Authorization: Bearer <access_token>
```

### Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Success message",
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "req_123456789"
  }
}
```

### Error Format

Error responses include detailed information:

```json
{
  "success": false,
  "error": "Error message",
  "errors": {
    "field": ["Validation error message"]
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "requestId": "req_123456789",
    "code": "VALIDATION_ERROR"
  }
}
```

### Rate Limiting

API endpoints are rate limited:

- **Global**: 1000 requests per minute
- **Per User**: 100 requests per minute
- **Authentication**: 10 requests per minute
- **File Upload**: 20 requests per minute

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Testing

### API Testing

Test API integration using the provided test utilities:

```typescript
import { mockApiResponses } from '@/utils/__tests__/testUtils'

// Mock API responses for testing
mockFetch({
  'POST /api/auth/login': mockApiResponses.login.success,
  'GET /api/properties': mockApiResponses.properties.list
})
```

### WebSocket Testing

Test WebSocket functionality:

```typescript
// Mock WebSocket events
webSocketService.emit('propertyUpdated', mockProperty)
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check JWT secret configuration
   - Verify token expiry settings
   - Ensure proper token storage

2. **API Connection Issues**
   - Verify backend URL configuration
   - Check CORS settings
   - Validate API key

3. **WebSocket Connection Issues**
   - Check WebSocket URL
   - Verify authentication token
   - Check firewall settings

4. **File Upload Issues**
   - Check file size limits
   - Verify allowed file types
   - Check storage configuration

### Debug Mode

Enable debug mode for detailed logging:

```bash
DEBUG=true npm run dev
```

## Support

For backend integration support:

- Check the [API Documentation](./API.md)
- Review [Error Codes](./ERROR_CODES.md)
- Contact the development team

---

This backend integration provides a robust foundation for the BUILD-CONNECT platform with comprehensive API services, real-time features, and offline support.
